2025-06-23 11:12:28,476 - ERROR - Failed to connect to qBittorrent: Client.__init__() got an unexpected keyword argument 'HTTPS'
2025-06-23 11:12:28,509 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-23 11:12:28,509 - INFO - [33mPress CTRL+C to quit[0m
2025-06-23 11:13:12,872 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:13:12,915 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-23 11:13:12,916 - INFO - [33mPress CTRL+C to quit[0m
2025-06-23 11:13:37,315 - INFO - 127.0.0.1 - - [23/Jun/2025 11:13:37] "GET / HTTP/1.1" 200 -
2025-06-23 11:13:37,417 - INFO - 127.0.0.1 - - [23/Jun/2025 11:13:37] "GET /static/js/app.js HTTP/1.1" 200 -
2025-06-23 11:13:37,911 - INFO - 127.0.0.1 - - [23/Jun/2025 11:13:37] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-23 11:13:48,150 - ERROR - Failed to connect to qBittorrent: Connection failed
2025-06-23 11:13:48,151 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:13:48,161 - INFO - Paused torrent: Test Torrent (Seeds: 10, Ratio: 3.00)
2025-06-23 11:13:48,162 - INFO - Checked 1 torrents. Paused: 0, Seeding: 1
2025-06-23 11:15:02,007 - INFO - 127.0.0.1 - - [23/Jun/2025 11:15:02] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-23 11:17:08,885 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:17:08,931 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-23 11:17:08,932 - INFO - [33mPress CTRL+C to quit[0m
2025-06-23 11:17:27,635 - INFO - 127.0.0.1 - - [23/Jun/2025 11:17:27] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:17:33,607 - INFO - 127.0.0.1 - - [23/Jun/2025 11:17:33] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:21:04,603 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:21:04,646 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-23 11:21:04,646 - INFO - [33mPress CTRL+C to quit[0m
2025-06-23 11:21:26,877 - INFO - 127.0.0.1 - - [23/Jun/2025 11:21:26] "GET /api/categories HTTP/1.1" 200 -
2025-06-23 11:21:33,173 - INFO - 127.0.0.1 - - [23/Jun/2025 11:21:33] "[31m[1mPOST /api/config HTTP/1.1[0m" 400 -
2025-06-23 11:21:39,011 - INFO - 127.0.0.1 - - [23/Jun/2025 11:21:39] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:21:45,761 - INFO - 127.0.0.1 - - [23/Jun/2025 11:21:45] "[31m[1mPOST /api/config HTTP/1.1[0m" 400 -
2025-06-23 11:22:23,955 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:22:24,016 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-23 11:22:24,016 - INFO - [33mPress CTRL+C to quit[0m
2025-06-23 11:22:43,821 - ERROR - Failed to update config: 400 Bad Request: The browser (or proxy) sent a request that this server could not understand.
2025-06-23 11:22:43,823 - INFO - 127.0.0.1 - - [23/Jun/2025 11:22:43] "[35m[1mPOST /api/config HTTP/1.1[0m" 500 -
2025-06-23 11:23:11,847 - INFO - 127.0.0.1 - - [23/Jun/2025 11:23:11] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:23:14,034 - INFO - 127.0.0.1 - - [23/Jun/2025 11:23:14] "GET /api/categories HTTP/1.1" 200 -
2025-06-23 11:23:16,071 - INFO - Configuration updated
2025-06-23 11:23:16,073 - INFO - 127.0.0.1 - - [23/Jun/2025 11:23:16] "POST /api/config HTTP/1.1" 200 -
2025-06-23 11:23:18,135 - INFO - 127.0.0.1 - - [23/Jun/2025 11:23:18] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:23:25,362 - INFO - 127.0.0.1 - - [23/Jun/2025 11:23:25] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:23:32,861 - INFO - 127.0.0.1 - - [23/Jun/2025 11:23:32] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:23:41,446 - INFO - Configuration updated
2025-06-23 11:23:41,447 - INFO - 127.0.0.1 - - [23/Jun/2025 11:23:41] "POST /api/config HTTP/1.1" 200 -
2025-06-23 11:23:47,150 - INFO - 127.0.0.1 - - [23/Jun/2025 11:23:47] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:26:09,879 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:26:10,108 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-23 11:26:10,109 - INFO - [33mPress CTRL+C to quit[0m
2025-06-23 11:26:10,115 - INFO -  * Restarting with stat
2025-06-23 11:26:11,102 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:26:11,162 - WARNING -  * Debugger is active!
2025-06-23 11:26:11,166 - INFO -  * Debugger PIN: 634-283-480
2025-06-23 11:26:14,227 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:14] "GET / HTTP/1.1" 200 -
2025-06-23 11:26:14,344 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:14] "GET /static/js/app.js HTTP/1.1" 200 -
2025-06-23 11:26:14,427 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:14] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:26:14,472 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:14] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:26:14,602 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:14] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 204 -
2025-06-23 11:26:19,675 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:19] "GET /api/config HTTP/1.1" 200 -
2025-06-23 11:26:30,837 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:30] "GET /api/categories HTTP/1.1" 200 -
2025-06-23 11:26:43,953 - INFO - Configuration updated
2025-06-23 11:26:43,955 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:43] "POST /api/config HTTP/1.1" 200 -
2025-06-23 11:26:43,968 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:43] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:26:44,034 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:44] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:26:44,439 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:44] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:26:44,504 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:44] "GET /api/torrents HTTP/1.1" 200 -
