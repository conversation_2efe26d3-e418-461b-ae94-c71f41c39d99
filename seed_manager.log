2025-06-23 11:12:28,476 - ERROR - Failed to connect to qBittorrent: Client.__init__() got an unexpected keyword argument 'HTTPS'
2025-06-23 11:12:28,509 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-23 11:12:28,509 - INFO - [33mPress CTRL+C to quit[0m
2025-06-23 11:13:12,872 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:13:12,915 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-23 11:13:12,916 - INFO - [33mPress CTRL+C to quit[0m
2025-06-23 11:13:37,315 - INFO - 127.0.0.1 - - [23/Jun/2025 11:13:37] "GET / HTTP/1.1" 200 -
2025-06-23 11:13:37,417 - INFO - 127.0.0.1 - - [23/Jun/2025 11:13:37] "GET /static/js/app.js HTTP/1.1" 200 -
2025-06-23 11:13:37,911 - INFO - 127.0.0.1 - - [23/Jun/2025 11:13:37] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-23 11:13:48,150 - ERROR - Failed to connect to qBittorrent: Connection failed
2025-06-23 11:13:48,151 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:13:48,161 - INFO - Paused torrent: Test Torrent (Seeds: 10, Ratio: 3.00)
2025-06-23 11:13:48,162 - INFO - Checked 1 torrents. Paused: 0, Seeding: 1
2025-06-23 11:15:02,007 - INFO - 127.0.0.1 - - [23/Jun/2025 11:15:02] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-23 11:17:08,885 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:17:08,931 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-23 11:17:08,932 - INFO - [33mPress CTRL+C to quit[0m
2025-06-23 11:17:27,635 - INFO - 127.0.0.1 - - [23/Jun/2025 11:17:27] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:17:33,607 - INFO - 127.0.0.1 - - [23/Jun/2025 11:17:33] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:21:04,603 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:21:04,646 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-23 11:21:04,646 - INFO - [33mPress CTRL+C to quit[0m
2025-06-23 11:21:26,877 - INFO - 127.0.0.1 - - [23/Jun/2025 11:21:26] "GET /api/categories HTTP/1.1" 200 -
2025-06-23 11:21:33,173 - INFO - 127.0.0.1 - - [23/Jun/2025 11:21:33] "[31m[1mPOST /api/config HTTP/1.1[0m" 400 -
2025-06-23 11:21:39,011 - INFO - 127.0.0.1 - - [23/Jun/2025 11:21:39] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:21:45,761 - INFO - 127.0.0.1 - - [23/Jun/2025 11:21:45] "[31m[1mPOST /api/config HTTP/1.1[0m" 400 -
2025-06-23 11:22:23,955 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:22:24,016 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-23 11:22:24,016 - INFO - [33mPress CTRL+C to quit[0m
2025-06-23 11:22:43,821 - ERROR - Failed to update config: 400 Bad Request: The browser (or proxy) sent a request that this server could not understand.
2025-06-23 11:22:43,823 - INFO - 127.0.0.1 - - [23/Jun/2025 11:22:43] "[35m[1mPOST /api/config HTTP/1.1[0m" 500 -
2025-06-23 11:23:11,847 - INFO - 127.0.0.1 - - [23/Jun/2025 11:23:11] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:23:14,034 - INFO - 127.0.0.1 - - [23/Jun/2025 11:23:14] "GET /api/categories HTTP/1.1" 200 -
2025-06-23 11:23:16,071 - INFO - Configuration updated
2025-06-23 11:23:16,073 - INFO - 127.0.0.1 - - [23/Jun/2025 11:23:16] "POST /api/config HTTP/1.1" 200 -
2025-06-23 11:23:18,135 - INFO - 127.0.0.1 - - [23/Jun/2025 11:23:18] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:23:25,362 - INFO - 127.0.0.1 - - [23/Jun/2025 11:23:25] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:23:32,861 - INFO - 127.0.0.1 - - [23/Jun/2025 11:23:32] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:23:41,446 - INFO - Configuration updated
2025-06-23 11:23:41,447 - INFO - 127.0.0.1 - - [23/Jun/2025 11:23:41] "POST /api/config HTTP/1.1" 200 -
2025-06-23 11:23:47,150 - INFO - 127.0.0.1 - - [23/Jun/2025 11:23:47] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:26:09,879 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:26:10,108 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-23 11:26:10,109 - INFO - [33mPress CTRL+C to quit[0m
2025-06-23 11:26:10,115 - INFO -  * Restarting with stat
2025-06-23 11:26:11,102 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:26:11,162 - WARNING -  * Debugger is active!
2025-06-23 11:26:11,166 - INFO -  * Debugger PIN: 634-283-480
2025-06-23 11:26:14,227 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:14] "GET / HTTP/1.1" 200 -
2025-06-23 11:26:14,344 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:14] "GET /static/js/app.js HTTP/1.1" 200 -
2025-06-23 11:26:14,427 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:14] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:26:14,472 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:14] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:26:14,602 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:14] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 204 -
2025-06-23 11:26:19,675 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:19] "GET /api/config HTTP/1.1" 200 -
2025-06-23 11:26:30,837 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:30] "GET /api/categories HTTP/1.1" 200 -
2025-06-23 11:26:43,953 - INFO - Configuration updated
2025-06-23 11:26:43,955 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:43] "POST /api/config HTTP/1.1" 200 -
2025-06-23 11:26:43,968 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:43] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:26:44,034 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:44] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:26:44,439 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:44] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:26:44,504 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:44] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:26:58,336 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:58] "GET / HTTP/1.1" 200 -
2025-06-23 11:26:58,413 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:58] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-23 11:26:58,446 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:58] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:26:58,482 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:58] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 204 -
2025-06-23 11:26:58,525 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:58] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:27:03,624 - INFO - 127.0.0.1 - - [23/Jun/2025 11:27:03] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:27:03,646 - INFO - 127.0.0.1 - - [23/Jun/2025 11:27:03] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:27:21,202 - INFO - 127.0.0.1 - - [23/Jun/2025 11:27:21] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-23 11:27:28,462 - INFO - 127.0.0.1 - - [23/Jun/2025 11:27:28] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:27:28,530 - INFO - 127.0.0.1 - - [23/Jun/2025 11:27:28] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:27:40,897 - INFO - 127.0.0.1 - - [23/Jun/2025 11:27:40] "POST /api/torrent/8b4ad9874f70e211ea865643937dadeb45c73aff/resume HTTP/1.1" 200 -
2025-06-23 11:27:40,996 - INFO - 127.0.0.1 - - [23/Jun/2025 11:27:40] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:27:41,028 - INFO - 127.0.0.1 - - [23/Jun/2025 11:27:41] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:27:47,524 - INFO - Force checking torrent: 8b4ad9874f70e211ea865643937dadeb45c73aff
2025-06-23 11:27:47,526 - INFO - 127.0.0.1 - - [23/Jun/2025 11:27:47] "POST /api/torrent/8b4ad9874f70e211ea865643937dadeb45c73aff/force-check HTTP/1.1" 200 -
2025-06-23 11:27:58,450 - INFO - 127.0.0.1 - - [23/Jun/2025 11:27:58] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:27:58,526 - INFO - 127.0.0.1 - - [23/Jun/2025 11:27:58] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:28:29,156 - INFO - 127.0.0.1 - - [23/Jun/2025 11:28:29] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:28:29,234 - INFO - 127.0.0.1 - - [23/Jun/2025 11:28:29] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:28:58,465 - INFO - 127.0.0.1 - - [23/Jun/2025 11:28:58] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:28:58,544 - INFO - 127.0.0.1 - - [23/Jun/2025 11:28:58] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:29:29,162 - INFO - 127.0.0.1 - - [23/Jun/2025 11:29:29] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:29:29,235 - INFO - 127.0.0.1 - - [23/Jun/2025 11:29:29] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:29:59,160 - INFO - 127.0.0.1 - - [23/Jun/2025 11:29:59] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:29:59,237 - INFO - 127.0.0.1 - - [23/Jun/2025 11:29:59] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:31:15,161 - INFO - 127.0.0.1 - - [23/Jun/2025 11:31:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:31:15,242 - INFO - 127.0.0.1 - - [23/Jun/2025 11:31:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:31:30,280 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:31:30,354 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-23 11:31:30,354 - INFO - [33mPress CTRL+C to quit[0m
2025-06-23 11:32:15,155 - INFO - 127.0.0.1 - - [23/Jun/2025 11:32:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:32:15,253 - INFO - 127.0.0.1 - - [23/Jun/2025 11:32:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:33:15,158 - INFO - 127.0.0.1 - - [23/Jun/2025 11:33:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:33:15,230 - INFO - 127.0.0.1 - - [23/Jun/2025 11:33:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:34:15,163 - INFO - 127.0.0.1 - - [23/Jun/2025 11:34:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:34:15,235 - INFO - 127.0.0.1 - - [23/Jun/2025 11:34:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:35:15,156 - INFO - 127.0.0.1 - - [23/Jun/2025 11:35:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:35:15,229 - INFO - 127.0.0.1 - - [23/Jun/2025 11:35:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:36:15,153 - INFO - 127.0.0.1 - - [23/Jun/2025 11:36:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:36:15,231 - INFO - 127.0.0.1 - - [23/Jun/2025 11:36:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:36:38,435 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:36:38,504 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-23 11:36:38,505 - INFO - [33mPress CTRL+C to quit[0m
2025-06-23 11:36:38,507 - INFO -  * Restarting with stat
2025-06-23 11:36:39,390 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:36:39,440 - WARNING -  * Debugger is active!
2025-06-23 11:36:39,444 - INFO -  * Debugger PIN: 634-283-480
2025-06-23 11:36:59,316 - INFO - 127.0.0.1 - - [23/Jun/2025 11:36:59] "GET /api/debug/torrents HTTP/1.1" 200 -
2025-06-23 11:37:15,090 - INFO -  * Detected change in 'C:\\Users\\<USER>\\Nextcloud\\DEVELOPMENT\\AUGMENTED\\seedManager\\app.py', reloading
2025-06-23 11:37:15,157 - INFO -  * Restarting with stat
2025-06-23 11:37:16,104 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:37:16,148 - WARNING -  * Debugger is active!
2025-06-23 11:37:16,151 - INFO -  * Debugger PIN: 634-283-480
2025-06-23 11:37:16,276 - INFO - 127.0.0.1 - - [23/Jun/2025 11:37:16] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:37:16,347 - INFO - 127.0.0.1 - - [23/Jun/2025 11:37:16] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:37:25,803 - ERROR - Failed to get debug data: Object of type method is not JSON serializable
2025-06-23 11:37:25,804 - INFO - 127.0.0.1 - - [23/Jun/2025 11:37:25] "GET /api/debug/torrents HTTP/1.1" 200 -
2025-06-23 11:37:35,903 - INFO -  * Detected change in 'C:\\Users\\<USER>\\Nextcloud\\DEVELOPMENT\\AUGMENTED\\seedManager\\app.py', reloading
2025-06-23 11:37:35,955 - INFO -  * Restarting with stat
2025-06-23 11:37:36,973 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:37:37,018 - WARNING -  * Debugger is active!
2025-06-23 11:37:37,021 - INFO -  * Debugger PIN: 634-283-480
2025-06-23 11:37:45,391 - ERROR - Failed to get debug data: Object of type method is not JSON serializable
2025-06-23 11:37:45,393 - INFO - 127.0.0.1 - - [23/Jun/2025 11:37:45] "GET /api/debug/torrents HTTP/1.1" 200 -
2025-06-23 11:37:57,967 - INFO -  * Detected change in 'C:\\Users\\<USER>\\Nextcloud\\DEVELOPMENT\\AUGMENTED\\seedManager\\app.py', reloading
2025-06-23 11:37:58,028 - INFO -  * Restarting with stat
2025-06-23 11:37:58,999 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:37:59,048 - WARNING -  * Debugger is active!
2025-06-23 11:37:59,052 - INFO -  * Debugger PIN: 634-283-480
2025-06-23 11:38:07,242 - INFO - 127.0.0.1 - - [23/Jun/2025 11:38:07] "GET /api/debug/torrents HTTP/1.1" 200 -
2025-06-23 11:38:15,165 - INFO - 127.0.0.1 - - [23/Jun/2025 11:38:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:38:15,248 - INFO - 127.0.0.1 - - [23/Jun/2025 11:38:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:38:19,164 - INFO - 127.0.0.1 - - [23/Jun/2025 11:38:19] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:38:26,506 - INFO - Checked 79 torrents. Paused: 0, Seeding: 2
2025-06-23 11:38:26,508 - INFO - 127.0.0.1 - - [23/Jun/2025 11:38:26] "POST /api/check HTTP/1.1" 200 -
2025-06-23 11:38:32,291 - INFO - 127.0.0.1 - - [23/Jun/2025 11:38:32] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:38:49,501 - INFO - 127.0.0.1 - - [23/Jun/2025 11:38:49] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:39:05,957 - INFO -  * Detected change in 'C:\\Users\\<USER>\\Nextcloud\\DEVELOPMENT\\AUGMENTED\\seedManager\\app.py', reloading
2025-06-23 11:39:06,018 - INFO -  * Restarting with stat
2025-06-23 11:39:06,827 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:39:06,881 - WARNING -  * Debugger is active!
2025-06-23 11:39:06,886 - INFO -  * Debugger PIN: 634-283-480
2025-06-23 11:39:15,154 - INFO - 127.0.0.1 - - [23/Jun/2025 11:39:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:39:15,255 - INFO - 127.0.0.1 - - [23/Jun/2025 11:39:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:39:17,574 - INFO - Force starting torrent: 8b4ad9874f70e211ea865643937dadeb45c73aff
2025-06-23 11:39:17,575 - INFO - 127.0.0.1 - - [23/Jun/2025 11:39:17] "POST /api/torrent/8b4ad9874f70e211ea865643937dadeb45c73aff/force-start HTTP/1.1" 200 -
2025-06-23 11:40:15,163 - INFO - 127.0.0.1 - - [23/Jun/2025 11:40:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:40:15,244 - INFO - 127.0.0.1 - - [23/Jun/2025 11:40:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:43:26,365 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:43:26,429 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-23 11:43:26,430 - INFO - [33mPress CTRL+C to quit[0m
2025-06-23 11:43:26,432 - INFO -  * Restarting with stat
2025-06-23 11:43:27,382 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:43:27,431 - WARNING -  * Debugger is active!
2025-06-23 11:43:27,436 - INFO -  * Debugger PIN: 634-283-480
2025-06-23 11:43:40,665 - INFO - 127.0.0.1 - - [23/Jun/2025 11:43:40] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:43:40,740 - INFO - 127.0.0.1 - - [23/Jun/2025 11:43:40] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:43:43,347 - INFO - 127.0.0.1 - - [23/Jun/2025 11:43:43] "GET / HTTP/1.1" 200 -
2025-06-23 11:43:43,464 - INFO - 127.0.0.1 - - [23/Jun/2025 11:43:43] "GET /static/js/app.js HTTP/1.1" 200 -
2025-06-23 11:43:43,482 - INFO - 127.0.0.1 - - [23/Jun/2025 11:43:43] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:43:43,544 - INFO - 127.0.0.1 - - [23/Jun/2025 11:43:43] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:43:43,566 - INFO - 127.0.0.1 - - [23/Jun/2025 11:43:43] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 204 -
2025-06-23 11:43:45,455 - INFO - 127.0.0.1 - - [23/Jun/2025 11:43:45] "GET / HTTP/1.1" 200 -
2025-06-23 11:43:45,512 - INFO - 127.0.0.1 - - [23/Jun/2025 11:43:45] "GET /static/js/app.js HTTP/1.1" 200 -
2025-06-23 11:43:45,575 - INFO - 127.0.0.1 - - [23/Jun/2025 11:43:45] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:43:45,651 - INFO - 127.0.0.1 - - [23/Jun/2025 11:43:45] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:43:45,683 - INFO - 127.0.0.1 - - [23/Jun/2025 11:43:45] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 204 -
2025-06-23 11:44:08,992 - INFO - 127.0.0.1 - - [23/Jun/2025 11:44:08] "GET / HTTP/1.1" 200 -
2025-06-23 11:44:09,034 - INFO - 127.0.0.1 - - [23/Jun/2025 11:44:09] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-23 11:44:09,318 - INFO - 127.0.0.1 - - [23/Jun/2025 11:44:09] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:44:09,387 - INFO - 127.0.0.1 - - [23/Jun/2025 11:44:09] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:44:09,657 - INFO - 127.0.0.1 - - [23/Jun/2025 11:44:09] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 204 -
2025-06-23 11:44:27,701 - INFO - Force checking torrent: 8b4ad9874f70e211ea865643937dadeb45c73aff
2025-06-23 11:44:27,703 - INFO - 127.0.0.1 - - [23/Jun/2025 11:44:27] "POST /api/torrent/8b4ad9874f70e211ea865643937dadeb45c73aff/force-check HTTP/1.1" 200 -
2025-06-23 11:44:34,916 - INFO - 127.0.0.1 - - [23/Jun/2025 11:44:34] "POST /api/torrent/8b4ad9874f70e211ea865643937dadeb45c73aff/resume HTTP/1.1" 200 -
2025-06-23 11:44:35,003 - INFO - 127.0.0.1 - - [23/Jun/2025 11:44:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:44:35,033 - INFO - 127.0.0.1 - - [23/Jun/2025 11:44:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:44:39,336 - INFO - 127.0.0.1 - - [23/Jun/2025 11:44:39] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:44:39,414 - INFO - 127.0.0.1 - - [23/Jun/2025 11:44:39] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:44:43,764 - INFO - Force starting torrent: 8b4ad9874f70e211ea865643937dadeb45c73aff
2025-06-23 11:44:43,766 - INFO - 127.0.0.1 - - [23/Jun/2025 11:44:43] "POST /api/torrent/8b4ad9874f70e211ea865643937dadeb45c73aff/force-start HTTP/1.1" 200 -
2025-06-23 11:44:43,842 - INFO - 127.0.0.1 - - [23/Jun/2025 11:44:43] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:44:43,866 - INFO - 127.0.0.1 - - [23/Jun/2025 11:44:43] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:44:55,685 - INFO - Force checking torrent: 8b4ad9874f70e211ea865643937dadeb45c73aff
2025-06-23 11:44:55,686 - INFO - 127.0.0.1 - - [23/Jun/2025 11:44:55] "POST /api/torrent/8b4ad9874f70e211ea865643937dadeb45c73aff/force-check HTTP/1.1" 200 -
2025-06-23 11:45:01,437 - INFO - 127.0.0.1 - - [23/Jun/2025 11:45:01] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-23 11:45:09,328 - INFO - 127.0.0.1 - - [23/Jun/2025 11:45:09] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:45:09,459 - INFO - 127.0.0.1 - - [23/Jun/2025 11:45:09] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:45:39,327 - INFO - 127.0.0.1 - - [23/Jun/2025 11:45:39] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:45:39,421 - INFO - 127.0.0.1 - - [23/Jun/2025 11:45:39] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:45:50,556 - INFO - 127.0.0.1 - - [23/Jun/2025 11:45:50] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-23 11:46:01,991 - INFO - Force checking torrent: 8b4ad9874f70e211ea865643937dadeb45c73aff
2025-06-23 11:46:01,992 - INFO - 127.0.0.1 - - [23/Jun/2025 11:46:01] "POST /api/torrent/8b4ad9874f70e211ea865643937dadeb45c73aff/force-check HTTP/1.1" 200 -
2025-06-23 11:46:09,332 - INFO - 127.0.0.1 - - [23/Jun/2025 11:46:09] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:46:09,419 - INFO - 127.0.0.1 - - [23/Jun/2025 11:46:09] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:46:40,157 - INFO - 127.0.0.1 - - [23/Jun/2025 11:46:40] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:46:40,225 - INFO - 127.0.0.1 - - [23/Jun/2025 11:46:40] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:47:10,147 - INFO - 127.0.0.1 - - [23/Jun/2025 11:47:10] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:47:10,223 - INFO - 127.0.0.1 - - [23/Jun/2025 11:47:10] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:47:37,960 - INFO -  * Detected change in 'C:\\Users\\<USER>\\Nextcloud\\DEVELOPMENT\\AUGMENTED\\seedManager\\app.py', reloading
2025-06-23 11:47:38,019 - INFO -  * Restarting with stat
2025-06-23 11:47:38,822 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:47:38,872 - WARNING -  * Debugger is active!
2025-06-23 11:47:38,876 - INFO -  * Debugger PIN: 634-283-480
2025-06-23 11:47:39,546 - INFO - 127.0.0.1 - - [23/Jun/2025 11:47:39] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:47:39,606 - ERROR - Failed to get torrents: 'SeedManager' object has no attribute '_update_stats'
2025-06-23 11:47:39,607 - INFO - 127.0.0.1 - - [23/Jun/2025 11:47:39] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:47:57,502 - INFO -  * Detected change in 'C:\\Users\\<USER>\\Nextcloud\\DEVELOPMENT\\AUGMENTED\\seedManager\\app.py', reloading
2025-06-23 11:47:57,553 - INFO -  * Restarting with stat
2025-06-23 11:47:58,351 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:47:58,406 - WARNING -  * Debugger is active!
2025-06-23 11:47:58,412 - INFO -  * Debugger PIN: 634-283-480
2025-06-23 11:48:10,156 - INFO - 127.0.0.1 - - [23/Jun/2025 11:48:10] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:48:10,233 - INFO - 127.0.0.1 - - [23/Jun/2025 11:48:10] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:48:21,644 - INFO -  * Detected change in 'C:\\Users\\<USER>\\Nextcloud\\DEVELOPMENT\\AUGMENTED\\seedManager\\app.py', reloading
2025-06-23 11:48:21,707 - INFO -  * Restarting with stat
2025-06-23 11:48:22,685 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:48:22,729 - WARNING -  * Debugger is active!
2025-06-23 11:48:22,733 - INFO -  * Debugger PIN: 634-283-480
2025-06-23 11:48:26,657 - INFO - Checked 79 torrents. Paused: 0, Seeding: 2
2025-06-23 11:48:39,004 - INFO - 127.0.0.1 - - [23/Jun/2025 11:48:39] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:48:40,146 - INFO - 127.0.0.1 - - [23/Jun/2025 11:48:40] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:48:40,228 - INFO - 127.0.0.1 - - [23/Jun/2025 11:48:40] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:48:52,404 - INFO - 127.0.0.1 - - [23/Jun/2025 11:48:52] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:49:01,678 - INFO - 127.0.0.1 - - [23/Jun/2025 11:49:01] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:49:15,164 - INFO - 127.0.0.1 - - [23/Jun/2025 11:49:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:49:15,235 - INFO - 127.0.0.1 - - [23/Jun/2025 11:49:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:50:15,159 - INFO - 127.0.0.1 - - [23/Jun/2025 11:50:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:50:15,239 - INFO - 127.0.0.1 - - [23/Jun/2025 11:50:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:50:26,035 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:50:26,100 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-23 11:50:26,101 - INFO - [33mPress CTRL+C to quit[0m
2025-06-23 11:50:26,104 - INFO -  * Restarting with stat
2025-06-23 11:50:27,075 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:50:27,124 - WARNING -  * Debugger is active!
2025-06-23 11:50:27,128 - INFO -  * Debugger PIN: 634-283-480
2025-06-23 11:51:15,166 - INFO - 127.0.0.1 - - [23/Jun/2025 11:51:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:51:15,241 - INFO - 127.0.0.1 - - [23/Jun/2025 11:51:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:51:32,917 - INFO - 127.0.0.1 - - [23/Jun/2025 11:51:32] "GET / HTTP/1.1" 200 -
2025-06-23 11:51:32,986 - INFO - 127.0.0.1 - - [23/Jun/2025 11:51:32] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-23 11:51:33,060 - INFO - 127.0.0.1 - - [23/Jun/2025 11:51:33] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-23 11:51:33,119 - INFO - 127.0.0.1 - - [23/Jun/2025 11:51:33] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:51:33,188 - INFO - 127.0.0.1 - - [23/Jun/2025 11:51:33] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:51:33,233 - INFO - 127.0.0.1 - - [23/Jun/2025 11:51:33] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 204 -
2025-06-23 11:52:00,261 - INFO - 127.0.0.1 - - [23/Jun/2025 11:52:00] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:52:00,305 - INFO - 127.0.0.1 - - [23/Jun/2025 11:52:00] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:52:03,121 - INFO - 127.0.0.1 - - [23/Jun/2025 11:52:03] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:52:03,196 - INFO - 127.0.0.1 - - [23/Jun/2025 11:52:03] "GET /api/torrents HTTP/1.1" 200 -
