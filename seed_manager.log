2025-06-23 11:12:28,476 - ERROR - Failed to connect to qBittorrent: Client.__init__() got an unexpected keyword argument 'HTTPS'
2025-06-23 11:12:28,509 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-23 11:12:28,509 - INFO - [33mPress CTRL+C to quit[0m
2025-06-23 11:13:12,872 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:13:12,915 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-23 11:13:12,916 - INFO - [33mPress CTRL+C to quit[0m
2025-06-23 11:13:37,315 - INFO - 127.0.0.1 - - [23/Jun/2025 11:13:37] "GET / HTTP/1.1" 200 -
2025-06-23 11:13:37,417 - INFO - 127.0.0.1 - - [23/Jun/2025 11:13:37] "GET /static/js/app.js HTTP/1.1" 200 -
2025-06-23 11:13:37,911 - INFO - 127.0.0.1 - - [23/Jun/2025 11:13:37] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-23 11:13:48,150 - ERROR - Failed to connect to qBittorrent: Connection failed
2025-06-23 11:13:48,151 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:13:48,161 - INFO - Paused torrent: Test Torrent (Seeds: 10, Ratio: 3.00)
2025-06-23 11:13:48,162 - INFO - Checked 1 torrents. Paused: 0, Seeding: 1
2025-06-23 11:15:02,007 - INFO - 127.0.0.1 - - [23/Jun/2025 11:15:02] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-23 11:17:08,885 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:17:08,931 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-23 11:17:08,932 - INFO - [33mPress CTRL+C to quit[0m
2025-06-23 11:17:27,635 - INFO - 127.0.0.1 - - [23/Jun/2025 11:17:27] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:17:33,607 - INFO - 127.0.0.1 - - [23/Jun/2025 11:17:33] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:21:04,603 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:21:04,646 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-23 11:21:04,646 - INFO - [33mPress CTRL+C to quit[0m
2025-06-23 11:21:26,877 - INFO - 127.0.0.1 - - [23/Jun/2025 11:21:26] "GET /api/categories HTTP/1.1" 200 -
2025-06-23 11:21:33,173 - INFO - 127.0.0.1 - - [23/Jun/2025 11:21:33] "[31m[1mPOST /api/config HTTP/1.1[0m" 400 -
2025-06-23 11:21:39,011 - INFO - 127.0.0.1 - - [23/Jun/2025 11:21:39] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:21:45,761 - INFO - 127.0.0.1 - - [23/Jun/2025 11:21:45] "[31m[1mPOST /api/config HTTP/1.1[0m" 400 -
2025-06-23 11:22:23,955 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:22:24,016 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-23 11:22:24,016 - INFO - [33mPress CTRL+C to quit[0m
2025-06-23 11:22:43,821 - ERROR - Failed to update config: 400 Bad Request: The browser (or proxy) sent a request that this server could not understand.
2025-06-23 11:22:43,823 - INFO - 127.0.0.1 - - [23/Jun/2025 11:22:43] "[35m[1mPOST /api/config HTTP/1.1[0m" 500 -
2025-06-23 11:23:11,847 - INFO - 127.0.0.1 - - [23/Jun/2025 11:23:11] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:23:14,034 - INFO - 127.0.0.1 - - [23/Jun/2025 11:23:14] "GET /api/categories HTTP/1.1" 200 -
2025-06-23 11:23:16,071 - INFO - Configuration updated
2025-06-23 11:23:16,073 - INFO - 127.0.0.1 - - [23/Jun/2025 11:23:16] "POST /api/config HTTP/1.1" 200 -
2025-06-23 11:23:18,135 - INFO - 127.0.0.1 - - [23/Jun/2025 11:23:18] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:23:25,362 - INFO - 127.0.0.1 - - [23/Jun/2025 11:23:25] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:23:32,861 - INFO - 127.0.0.1 - - [23/Jun/2025 11:23:32] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:23:41,446 - INFO - Configuration updated
2025-06-23 11:23:41,447 - INFO - 127.0.0.1 - - [23/Jun/2025 11:23:41] "POST /api/config HTTP/1.1" 200 -
2025-06-23 11:23:47,150 - INFO - 127.0.0.1 - - [23/Jun/2025 11:23:47] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:26:09,879 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:26:10,108 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-23 11:26:10,109 - INFO - [33mPress CTRL+C to quit[0m
2025-06-23 11:26:10,115 - INFO -  * Restarting with stat
2025-06-23 11:26:11,102 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:26:11,162 - WARNING -  * Debugger is active!
2025-06-23 11:26:11,166 - INFO -  * Debugger PIN: 634-283-480
2025-06-23 11:26:14,227 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:14] "GET / HTTP/1.1" 200 -
2025-06-23 11:26:14,344 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:14] "GET /static/js/app.js HTTP/1.1" 200 -
2025-06-23 11:26:14,427 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:14] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:26:14,472 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:14] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:26:14,602 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:14] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 204 -
2025-06-23 11:26:19,675 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:19] "GET /api/config HTTP/1.1" 200 -
2025-06-23 11:26:30,837 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:30] "GET /api/categories HTTP/1.1" 200 -
2025-06-23 11:26:43,953 - INFO - Configuration updated
2025-06-23 11:26:43,955 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:43] "POST /api/config HTTP/1.1" 200 -
2025-06-23 11:26:43,968 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:43] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:26:44,034 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:44] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:26:44,439 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:44] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:26:44,504 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:44] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:26:58,336 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:58] "GET / HTTP/1.1" 200 -
2025-06-23 11:26:58,413 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:58] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-23 11:26:58,446 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:58] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:26:58,482 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:58] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 204 -
2025-06-23 11:26:58,525 - INFO - 127.0.0.1 - - [23/Jun/2025 11:26:58] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:27:03,624 - INFO - 127.0.0.1 - - [23/Jun/2025 11:27:03] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:27:03,646 - INFO - 127.0.0.1 - - [23/Jun/2025 11:27:03] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:27:21,202 - INFO - 127.0.0.1 - - [23/Jun/2025 11:27:21] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-23 11:27:28,462 - INFO - 127.0.0.1 - - [23/Jun/2025 11:27:28] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:27:28,530 - INFO - 127.0.0.1 - - [23/Jun/2025 11:27:28] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:27:40,897 - INFO - 127.0.0.1 - - [23/Jun/2025 11:27:40] "POST /api/torrent/8b4ad9874f70e211ea865643937dadeb45c73aff/resume HTTP/1.1" 200 -
2025-06-23 11:27:40,996 - INFO - 127.0.0.1 - - [23/Jun/2025 11:27:40] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:27:41,028 - INFO - 127.0.0.1 - - [23/Jun/2025 11:27:41] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:27:47,524 - INFO - Force checking torrent: 8b4ad9874f70e211ea865643937dadeb45c73aff
2025-06-23 11:27:47,526 - INFO - 127.0.0.1 - - [23/Jun/2025 11:27:47] "POST /api/torrent/8b4ad9874f70e211ea865643937dadeb45c73aff/force-check HTTP/1.1" 200 -
2025-06-23 11:27:58,450 - INFO - 127.0.0.1 - - [23/Jun/2025 11:27:58] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:27:58,526 - INFO - 127.0.0.1 - - [23/Jun/2025 11:27:58] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:28:29,156 - INFO - 127.0.0.1 - - [23/Jun/2025 11:28:29] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:28:29,234 - INFO - 127.0.0.1 - - [23/Jun/2025 11:28:29] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:28:58,465 - INFO - 127.0.0.1 - - [23/Jun/2025 11:28:58] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:28:58,544 - INFO - 127.0.0.1 - - [23/Jun/2025 11:28:58] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:29:29,162 - INFO - 127.0.0.1 - - [23/Jun/2025 11:29:29] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:29:29,235 - INFO - 127.0.0.1 - - [23/Jun/2025 11:29:29] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:29:59,160 - INFO - 127.0.0.1 - - [23/Jun/2025 11:29:59] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:29:59,237 - INFO - 127.0.0.1 - - [23/Jun/2025 11:29:59] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:31:15,161 - INFO - 127.0.0.1 - - [23/Jun/2025 11:31:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:31:15,242 - INFO - 127.0.0.1 - - [23/Jun/2025 11:31:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:31:30,280 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:31:30,354 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-23 11:31:30,354 - INFO - [33mPress CTRL+C to quit[0m
2025-06-23 11:32:15,155 - INFO - 127.0.0.1 - - [23/Jun/2025 11:32:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:32:15,253 - INFO - 127.0.0.1 - - [23/Jun/2025 11:32:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:33:15,158 - INFO - 127.0.0.1 - - [23/Jun/2025 11:33:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:33:15,230 - INFO - 127.0.0.1 - - [23/Jun/2025 11:33:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:34:15,163 - INFO - 127.0.0.1 - - [23/Jun/2025 11:34:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:34:15,235 - INFO - 127.0.0.1 - - [23/Jun/2025 11:34:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:35:15,156 - INFO - 127.0.0.1 - - [23/Jun/2025 11:35:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:35:15,229 - INFO - 127.0.0.1 - - [23/Jun/2025 11:35:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:36:15,153 - INFO - 127.0.0.1 - - [23/Jun/2025 11:36:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:36:15,231 - INFO - 127.0.0.1 - - [23/Jun/2025 11:36:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:36:38,435 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:36:38,504 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-23 11:36:38,505 - INFO - [33mPress CTRL+C to quit[0m
2025-06-23 11:36:38,507 - INFO -  * Restarting with stat
2025-06-23 11:36:39,390 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:36:39,440 - WARNING -  * Debugger is active!
2025-06-23 11:36:39,444 - INFO -  * Debugger PIN: 634-283-480
2025-06-23 11:36:59,316 - INFO - 127.0.0.1 - - [23/Jun/2025 11:36:59] "GET /api/debug/torrents HTTP/1.1" 200 -
2025-06-23 11:37:15,090 - INFO -  * Detected change in 'C:\\Users\\<USER>\\Nextcloud\\DEVELOPMENT\\AUGMENTED\\seedManager\\app.py', reloading
2025-06-23 11:37:15,157 - INFO -  * Restarting with stat
2025-06-23 11:37:16,104 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:37:16,148 - WARNING -  * Debugger is active!
2025-06-23 11:37:16,151 - INFO -  * Debugger PIN: 634-283-480
2025-06-23 11:37:16,276 - INFO - 127.0.0.1 - - [23/Jun/2025 11:37:16] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:37:16,347 - INFO - 127.0.0.1 - - [23/Jun/2025 11:37:16] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:37:25,803 - ERROR - Failed to get debug data: Object of type method is not JSON serializable
2025-06-23 11:37:25,804 - INFO - 127.0.0.1 - - [23/Jun/2025 11:37:25] "GET /api/debug/torrents HTTP/1.1" 200 -
2025-06-23 11:37:35,903 - INFO -  * Detected change in 'C:\\Users\\<USER>\\Nextcloud\\DEVELOPMENT\\AUGMENTED\\seedManager\\app.py', reloading
2025-06-23 11:37:35,955 - INFO -  * Restarting with stat
2025-06-23 11:37:36,973 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:37:37,018 - WARNING -  * Debugger is active!
2025-06-23 11:37:37,021 - INFO -  * Debugger PIN: 634-283-480
2025-06-23 11:37:45,391 - ERROR - Failed to get debug data: Object of type method is not JSON serializable
2025-06-23 11:37:45,393 - INFO - 127.0.0.1 - - [23/Jun/2025 11:37:45] "GET /api/debug/torrents HTTP/1.1" 200 -
2025-06-23 11:37:57,967 - INFO -  * Detected change in 'C:\\Users\\<USER>\\Nextcloud\\DEVELOPMENT\\AUGMENTED\\seedManager\\app.py', reloading
2025-06-23 11:37:58,028 - INFO -  * Restarting with stat
2025-06-23 11:37:58,999 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:37:59,048 - WARNING -  * Debugger is active!
2025-06-23 11:37:59,052 - INFO -  * Debugger PIN: 634-283-480
2025-06-23 11:38:07,242 - INFO - 127.0.0.1 - - [23/Jun/2025 11:38:07] "GET /api/debug/torrents HTTP/1.1" 200 -
2025-06-23 11:38:15,165 - INFO - 127.0.0.1 - - [23/Jun/2025 11:38:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:38:15,248 - INFO - 127.0.0.1 - - [23/Jun/2025 11:38:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:38:19,164 - INFO - 127.0.0.1 - - [23/Jun/2025 11:38:19] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:38:26,506 - INFO - Checked 79 torrents. Paused: 0, Seeding: 2
2025-06-23 11:38:26,508 - INFO - 127.0.0.1 - - [23/Jun/2025 11:38:26] "POST /api/check HTTP/1.1" 200 -
2025-06-23 11:38:32,291 - INFO - 127.0.0.1 - - [23/Jun/2025 11:38:32] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:38:49,501 - INFO - 127.0.0.1 - - [23/Jun/2025 11:38:49] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:39:05,957 - INFO -  * Detected change in 'C:\\Users\\<USER>\\Nextcloud\\DEVELOPMENT\\AUGMENTED\\seedManager\\app.py', reloading
2025-06-23 11:39:06,018 - INFO -  * Restarting with stat
2025-06-23 11:39:06,827 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:39:06,881 - WARNING -  * Debugger is active!
2025-06-23 11:39:06,886 - INFO -  * Debugger PIN: 634-283-480
2025-06-23 11:39:15,154 - INFO - 127.0.0.1 - - [23/Jun/2025 11:39:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:39:15,255 - INFO - 127.0.0.1 - - [23/Jun/2025 11:39:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:39:17,574 - INFO - Force starting torrent: 8b4ad9874f70e211ea865643937dadeb45c73aff
2025-06-23 11:39:17,575 - INFO - 127.0.0.1 - - [23/Jun/2025 11:39:17] "POST /api/torrent/8b4ad9874f70e211ea865643937dadeb45c73aff/force-start HTTP/1.1" 200 -
2025-06-23 11:40:15,163 - INFO - 127.0.0.1 - - [23/Jun/2025 11:40:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:40:15,244 - INFO - 127.0.0.1 - - [23/Jun/2025 11:40:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:43:26,365 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:43:26,429 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-23 11:43:26,430 - INFO - [33mPress CTRL+C to quit[0m
2025-06-23 11:43:26,432 - INFO -  * Restarting with stat
2025-06-23 11:43:27,382 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:43:27,431 - WARNING -  * Debugger is active!
2025-06-23 11:43:27,436 - INFO -  * Debugger PIN: 634-283-480
2025-06-23 11:43:40,665 - INFO - 127.0.0.1 - - [23/Jun/2025 11:43:40] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:43:40,740 - INFO - 127.0.0.1 - - [23/Jun/2025 11:43:40] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:43:43,347 - INFO - 127.0.0.1 - - [23/Jun/2025 11:43:43] "GET / HTTP/1.1" 200 -
2025-06-23 11:43:43,464 - INFO - 127.0.0.1 - - [23/Jun/2025 11:43:43] "GET /static/js/app.js HTTP/1.1" 200 -
2025-06-23 11:43:43,482 - INFO - 127.0.0.1 - - [23/Jun/2025 11:43:43] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:43:43,544 - INFO - 127.0.0.1 - - [23/Jun/2025 11:43:43] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:43:43,566 - INFO - 127.0.0.1 - - [23/Jun/2025 11:43:43] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 204 -
2025-06-23 11:43:45,455 - INFO - 127.0.0.1 - - [23/Jun/2025 11:43:45] "GET / HTTP/1.1" 200 -
2025-06-23 11:43:45,512 - INFO - 127.0.0.1 - - [23/Jun/2025 11:43:45] "GET /static/js/app.js HTTP/1.1" 200 -
2025-06-23 11:43:45,575 - INFO - 127.0.0.1 - - [23/Jun/2025 11:43:45] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:43:45,651 - INFO - 127.0.0.1 - - [23/Jun/2025 11:43:45] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:43:45,683 - INFO - 127.0.0.1 - - [23/Jun/2025 11:43:45] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 204 -
2025-06-23 11:44:08,992 - INFO - 127.0.0.1 - - [23/Jun/2025 11:44:08] "GET / HTTP/1.1" 200 -
2025-06-23 11:44:09,034 - INFO - 127.0.0.1 - - [23/Jun/2025 11:44:09] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-23 11:44:09,318 - INFO - 127.0.0.1 - - [23/Jun/2025 11:44:09] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:44:09,387 - INFO - 127.0.0.1 - - [23/Jun/2025 11:44:09] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:44:09,657 - INFO - 127.0.0.1 - - [23/Jun/2025 11:44:09] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 204 -
2025-06-23 11:44:27,701 - INFO - Force checking torrent: 8b4ad9874f70e211ea865643937dadeb45c73aff
2025-06-23 11:44:27,703 - INFO - 127.0.0.1 - - [23/Jun/2025 11:44:27] "POST /api/torrent/8b4ad9874f70e211ea865643937dadeb45c73aff/force-check HTTP/1.1" 200 -
2025-06-23 11:44:34,916 - INFO - 127.0.0.1 - - [23/Jun/2025 11:44:34] "POST /api/torrent/8b4ad9874f70e211ea865643937dadeb45c73aff/resume HTTP/1.1" 200 -
2025-06-23 11:44:35,003 - INFO - 127.0.0.1 - - [23/Jun/2025 11:44:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:44:35,033 - INFO - 127.0.0.1 - - [23/Jun/2025 11:44:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:44:39,336 - INFO - 127.0.0.1 - - [23/Jun/2025 11:44:39] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:44:39,414 - INFO - 127.0.0.1 - - [23/Jun/2025 11:44:39] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:44:43,764 - INFO - Force starting torrent: 8b4ad9874f70e211ea865643937dadeb45c73aff
2025-06-23 11:44:43,766 - INFO - 127.0.0.1 - - [23/Jun/2025 11:44:43] "POST /api/torrent/8b4ad9874f70e211ea865643937dadeb45c73aff/force-start HTTP/1.1" 200 -
2025-06-23 11:44:43,842 - INFO - 127.0.0.1 - - [23/Jun/2025 11:44:43] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:44:43,866 - INFO - 127.0.0.1 - - [23/Jun/2025 11:44:43] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:44:55,685 - INFO - Force checking torrent: 8b4ad9874f70e211ea865643937dadeb45c73aff
2025-06-23 11:44:55,686 - INFO - 127.0.0.1 - - [23/Jun/2025 11:44:55] "POST /api/torrent/8b4ad9874f70e211ea865643937dadeb45c73aff/force-check HTTP/1.1" 200 -
2025-06-23 11:45:01,437 - INFO - 127.0.0.1 - - [23/Jun/2025 11:45:01] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-23 11:45:09,328 - INFO - 127.0.0.1 - - [23/Jun/2025 11:45:09] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:45:09,459 - INFO - 127.0.0.1 - - [23/Jun/2025 11:45:09] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:45:39,327 - INFO - 127.0.0.1 - - [23/Jun/2025 11:45:39] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:45:39,421 - INFO - 127.0.0.1 - - [23/Jun/2025 11:45:39] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:45:50,556 - INFO - 127.0.0.1 - - [23/Jun/2025 11:45:50] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-23 11:46:01,991 - INFO - Force checking torrent: 8b4ad9874f70e211ea865643937dadeb45c73aff
2025-06-23 11:46:01,992 - INFO - 127.0.0.1 - - [23/Jun/2025 11:46:01] "POST /api/torrent/8b4ad9874f70e211ea865643937dadeb45c73aff/force-check HTTP/1.1" 200 -
2025-06-23 11:46:09,332 - INFO - 127.0.0.1 - - [23/Jun/2025 11:46:09] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:46:09,419 - INFO - 127.0.0.1 - - [23/Jun/2025 11:46:09] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:46:40,157 - INFO - 127.0.0.1 - - [23/Jun/2025 11:46:40] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:46:40,225 - INFO - 127.0.0.1 - - [23/Jun/2025 11:46:40] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:47:10,147 - INFO - 127.0.0.1 - - [23/Jun/2025 11:47:10] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:47:10,223 - INFO - 127.0.0.1 - - [23/Jun/2025 11:47:10] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:47:37,960 - INFO -  * Detected change in 'C:\\Users\\<USER>\\Nextcloud\\DEVELOPMENT\\AUGMENTED\\seedManager\\app.py', reloading
2025-06-23 11:47:38,019 - INFO -  * Restarting with stat
2025-06-23 11:47:38,822 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:47:38,872 - WARNING -  * Debugger is active!
2025-06-23 11:47:38,876 - INFO -  * Debugger PIN: 634-283-480
2025-06-23 11:47:39,546 - INFO - 127.0.0.1 - - [23/Jun/2025 11:47:39] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:47:39,606 - ERROR - Failed to get torrents: 'SeedManager' object has no attribute '_update_stats'
2025-06-23 11:47:39,607 - INFO - 127.0.0.1 - - [23/Jun/2025 11:47:39] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:47:57,502 - INFO -  * Detected change in 'C:\\Users\\<USER>\\Nextcloud\\DEVELOPMENT\\AUGMENTED\\seedManager\\app.py', reloading
2025-06-23 11:47:57,553 - INFO -  * Restarting with stat
2025-06-23 11:47:58,351 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:47:58,406 - WARNING -  * Debugger is active!
2025-06-23 11:47:58,412 - INFO -  * Debugger PIN: 634-283-480
2025-06-23 11:48:10,156 - INFO - 127.0.0.1 - - [23/Jun/2025 11:48:10] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:48:10,233 - INFO - 127.0.0.1 - - [23/Jun/2025 11:48:10] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:48:21,644 - INFO -  * Detected change in 'C:\\Users\\<USER>\\Nextcloud\\DEVELOPMENT\\AUGMENTED\\seedManager\\app.py', reloading
2025-06-23 11:48:21,707 - INFO -  * Restarting with stat
2025-06-23 11:48:22,685 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:48:22,729 - WARNING -  * Debugger is active!
2025-06-23 11:48:22,733 - INFO -  * Debugger PIN: 634-283-480
2025-06-23 11:48:26,657 - INFO - Checked 79 torrents. Paused: 0, Seeding: 2
2025-06-23 11:48:39,004 - INFO - 127.0.0.1 - - [23/Jun/2025 11:48:39] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:48:40,146 - INFO - 127.0.0.1 - - [23/Jun/2025 11:48:40] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:48:40,228 - INFO - 127.0.0.1 - - [23/Jun/2025 11:48:40] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:48:52,404 - INFO - 127.0.0.1 - - [23/Jun/2025 11:48:52] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:49:01,678 - INFO - 127.0.0.1 - - [23/Jun/2025 11:49:01] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:49:15,164 - INFO - 127.0.0.1 - - [23/Jun/2025 11:49:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:49:15,235 - INFO - 127.0.0.1 - - [23/Jun/2025 11:49:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:50:15,159 - INFO - 127.0.0.1 - - [23/Jun/2025 11:50:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:50:15,239 - INFO - 127.0.0.1 - - [23/Jun/2025 11:50:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:50:26,035 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:50:26,100 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-23 11:50:26,101 - INFO - [33mPress CTRL+C to quit[0m
2025-06-23 11:50:26,104 - INFO -  * Restarting with stat
2025-06-23 11:50:27,075 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:50:27,124 - WARNING -  * Debugger is active!
2025-06-23 11:50:27,128 - INFO -  * Debugger PIN: 634-283-480
2025-06-23 11:51:15,166 - INFO - 127.0.0.1 - - [23/Jun/2025 11:51:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:51:15,241 - INFO - 127.0.0.1 - - [23/Jun/2025 11:51:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:51:32,917 - INFO - 127.0.0.1 - - [23/Jun/2025 11:51:32] "GET / HTTP/1.1" 200 -
2025-06-23 11:51:32,986 - INFO - 127.0.0.1 - - [23/Jun/2025 11:51:32] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-06-23 11:51:33,060 - INFO - 127.0.0.1 - - [23/Jun/2025 11:51:33] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-23 11:51:33,119 - INFO - 127.0.0.1 - - [23/Jun/2025 11:51:33] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:51:33,188 - INFO - 127.0.0.1 - - [23/Jun/2025 11:51:33] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:51:33,233 - INFO - 127.0.0.1 - - [23/Jun/2025 11:51:33] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 204 -
2025-06-23 11:52:00,261 - INFO - 127.0.0.1 - - [23/Jun/2025 11:52:00] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:52:00,305 - INFO - 127.0.0.1 - - [23/Jun/2025 11:52:00] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:52:03,121 - INFO - 127.0.0.1 - - [23/Jun/2025 11:52:03] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:52:03,196 - INFO - 127.0.0.1 - - [23/Jun/2025 11:52:03] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:52:33,149 - INFO - 127.0.0.1 - - [23/Jun/2025 11:52:33] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:52:33,220 - INFO - 127.0.0.1 - - [23/Jun/2025 11:52:33] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:53:03,148 - INFO - 127.0.0.1 - - [23/Jun/2025 11:53:03] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:53:03,188 - INFO -  * Detected change in 'C:\\Users\\<USER>\\Nextcloud\\DEVELOPMENT\\AUGMENTED\\seedManager\\app.py', reloading
2025-06-23 11:53:03,236 - INFO -  * Restarting with stat
2025-06-23 11:53:04,109 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 11:53:04,156 - WARNING -  * Debugger is active!
2025-06-23 11:53:04,162 - INFO -  * Debugger PIN: 634-283-480
2025-06-23 11:53:33,160 - INFO - 127.0.0.1 - - [23/Jun/2025 11:53:33] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:53:33,225 - INFO - 127.0.0.1 - - [23/Jun/2025 11:53:33] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:54:03,162 - INFO - 127.0.0.1 - - [23/Jun/2025 11:54:03] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:54:03,232 - INFO - 127.0.0.1 - - [23/Jun/2025 11:54:03] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:54:33,160 - INFO - 127.0.0.1 - - [23/Jun/2025 11:54:33] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:54:33,245 - INFO - 127.0.0.1 - - [23/Jun/2025 11:54:33] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:55:15,152 - INFO - 127.0.0.1 - - [23/Jun/2025 11:55:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:55:15,225 - INFO - 127.0.0.1 - - [23/Jun/2025 11:55:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:55:26,429 - INFO - Checked 79 torrents. Paused: 0, Seeding: 76
2025-06-23 11:55:44,412 - INFO - 127.0.0.1 - - [23/Jun/2025 11:55:44] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:55:53,219 - INFO - 127.0.0.1 - - [23/Jun/2025 11:55:53] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:56:04,952 - INFO - 127.0.0.1 - - [23/Jun/2025 11:56:04] "GET / HTTP/1.1" 200 -
2025-06-23 11:56:05,043 - INFO - 127.0.0.1 - - [23/Jun/2025 11:56:05] "GET /static/js/app.js HTTP/1.1" 200 -
2025-06-23 11:56:05,247 - INFO - 127.0.0.1 - - [23/Jun/2025 11:56:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:56:05,633 - INFO - 127.0.0.1 - - [23/Jun/2025 11:56:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:56:05,781 - INFO - 127.0.0.1 - - [23/Jun/2025 11:56:05] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 204 -
2025-06-23 11:56:15,159 - INFO - 127.0.0.1 - - [23/Jun/2025 11:56:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:56:15,247 - INFO - 127.0.0.1 - - [23/Jun/2025 11:56:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:56:35,569 - INFO - 127.0.0.1 - - [23/Jun/2025 11:56:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:56:35,658 - INFO - 127.0.0.1 - - [23/Jun/2025 11:56:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:57:05,253 - INFO - 127.0.0.1 - - [23/Jun/2025 11:57:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:57:05,641 - INFO - 127.0.0.1 - - [23/Jun/2025 11:57:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:57:15,155 - INFO - 127.0.0.1 - - [23/Jun/2025 11:57:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:57:15,236 - INFO - 127.0.0.1 - - [23/Jun/2025 11:57:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:57:35,560 - INFO - 127.0.0.1 - - [23/Jun/2025 11:57:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:57:35,636 - INFO - 127.0.0.1 - - [23/Jun/2025 11:57:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:58:04,422 - INFO - Checked 79 torrents. Paused: 0, Seeding: 76
2025-06-23 11:58:05,254 - INFO - 127.0.0.1 - - [23/Jun/2025 11:58:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:58:05,638 - INFO - 127.0.0.1 - - [23/Jun/2025 11:58:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:58:15,151 - INFO - 127.0.0.1 - - [23/Jun/2025 11:58:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:58:15,244 - INFO - 127.0.0.1 - - [23/Jun/2025 11:58:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:58:35,570 - INFO - 127.0.0.1 - - [23/Jun/2025 11:58:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:58:35,649 - INFO - 127.0.0.1 - - [23/Jun/2025 11:58:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:59:05,254 - INFO - 127.0.0.1 - - [23/Jun/2025 11:59:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:59:05,636 - INFO - 127.0.0.1 - - [23/Jun/2025 11:59:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:59:15,166 - INFO - 127.0.0.1 - - [23/Jun/2025 11:59:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:59:15,251 - INFO - 127.0.0.1 - - [23/Jun/2025 11:59:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 11:59:35,567 - INFO - 127.0.0.1 - - [23/Jun/2025 11:59:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 11:59:35,650 - INFO - 127.0.0.1 - - [23/Jun/2025 11:59:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:00:05,262 - INFO - 127.0.0.1 - - [23/Jun/2025 12:00:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:00:05,881 - ERROR - Failed to get torrents: <!DOCTYPE html>
<!--[if lt IE 7]> <html class="no-js ie6 oldie" lang="en-US"> <![endif]-->
<!--[if IE 7]>    <html class="no-js ie7 oldie" lang="en-US"> <![endif]-->
<!--[if IE 8]>    <html class="no-js ie8 oldie" lang="en-US"> <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en-US"> <!--<![endif]-->
<head>

<title>wikizell.com | 502: Bad gateway</title>
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=Edge" />
<meta name="robots" content="noindex, nofollow" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<link rel="stylesheet" id="cf_styles-css" href="/cdn-cgi/styles/main.css" />
</head>
<body>
<div id="cf-wrapper">
    <div id="cf-error-details" class="p-0">
        <header class="mx-auto pt-10 lg:pt-6 lg:px-8 w-240 lg:w-full mb-8">
            <h1 class="inline-block sm:block sm:mb-2 font-light text-60 lg:text-4xl text-black-dark leading-tight mr-2">
                <span class="inline-block">Bad gateway</span>
                <span class="code-label">Error code 502</span>
            </h1>
            <div>
                Visit <a href="https://www.cloudflare.com/5xx-error-landing?utm_source=errorcode_502&utm_campaign=qbt.wikizell.com" target="_blank" rel="noopener noreferrer">cloudflare.com</a> for more information.
            </div>
            <div class="mt-3">2025-06-23 10:00:08 UTC</div>
        </header>
        <div class="my-8 bg-gradient-gray">
            <div class="w-240 lg:w-full mx-auto">
                <div class="clearfix md:px-8">
                    <div id="cf-browser-status" class=" relative w-1/3 md:w-full py-15 md:p-0 md:py-8 md:text-left md:border-solid md:border-0 md:border-b md:border-gray-400 overflow-hidden float-left md:float-none text-center">
  <div class="relative mb-10 md:m-0">
    
    <span class="cf-icon-browser block md:hidden h-20 bg-center bg-no-repeat"></span>
    <span class="cf-icon-ok w-12 h-12 absolute left-1/2 md:left-auto md:right-0 md:top-0 -ml-6 -bottom-4"></span>
    
  </div>
  <span class="md:block w-full truncate">You</span>
  <h3 class="md:inline-block mt-3 md:mt-0 text-2xl text-gray-600 font-light leading-1.3">
  
    Browser
  
  </h3>
  
  <span class="leading-1.3 text-2xl text-green-success">Working</span>
  
</div>
                    <div id="cf-cloudflare-status" class=" relative w-1/3 md:w-full py-15 md:p-0 md:py-8 md:text-left md:border-solid md:border-0 md:border-b md:border-gray-400 overflow-hidden float-left md:float-none text-center">
  <div class="relative mb-10 md:m-0">
    <a href="https://www.cloudflare.com/5xx-error-landing?utm_source=errorcode_502&#38;utm_campaign=qbt.wikizell.com" target="_blank" rel="noopener noreferrer">
    <span class="cf-icon-cloud block md:hidden h-20 bg-center bg-no-repeat"></span>
    <span class="cf-icon-ok w-12 h-12 absolute left-1/2 md:left-auto md:right-0 md:top-0 -ml-6 -bottom-4"></span>
    </a>
  </div>
  <span class="md:block w-full truncate">Amsterdam</span>
  <h3 class="md:inline-block mt-3 md:mt-0 text-2xl text-gray-600 font-light leading-1.3">
  <a href="https://www.cloudflare.com/5xx-error-landing?utm_source=errorcode_502&utm_campaign=qbt.wikizell.com" target="_blank" rel="noopener noreferrer">
    Cloudflare
  </a>
  </h3>
  
  <span class="leading-1.3 text-2xl text-green-success">Working</span>
  
</div>
                    <div id="cf-host-status" class="cf-error-source relative w-1/3 md:w-full py-15 md:p-0 md:py-8 md:text-left md:border-solid md:border-0 md:border-b md:border-gray-400 overflow-hidden float-left md:float-none text-center">
  <div class="relative mb-10 md:m-0">
    
    <span class="cf-icon-server block md:hidden h-20 bg-center bg-no-repeat"></span>
    <span class="cf-icon-error w-12 h-12 absolute left-1/2 md:left-auto md:right-0 md:top-0 -ml-6 -bottom-4"></span>
    
  </div>
  <span class="md:block w-full truncate">qbt.wikizell.com</span>
  <h3 class="md:inline-block mt-3 md:mt-0 text-2xl text-gray-600 font-light leading-1.3">
  
    Host
  
  </h3>
  
  <span class="leading-1.3 text-2xl text-red-error">Error</span>
  
</div>
                </div>
            </div>
        </div>

        <div class="w-240 lg:w-full mx-auto mb-8 lg:px-8">
            <div class="clearfix">
                <div class="w-1/2 md:w-full float-left pr-6 md:pb-10 md:pr-0 leading-relaxed">
                    <h2 class="text-3xl font-normal leading-1.3 mb-4">What happened?</h2>
                    <p>The web server reported a bad gateway error.</p>
                </div>
                <div class="w-1/2 md:w-full float-left leading-relaxed">
                    <h2 class="text-3xl font-normal leading-1.3 mb-4">What can I do?</h2>
                    <p class="mb-6">Please try again in a few minutes.</p>
                </div>
            </div>
        </div>

        <div class="cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300">
    <p class="text-13">
      <span class="cf-footer-item sm:block sm:mb-1">Cloudflare Ray ID: <strong class="font-semibold">954322fddadc663d</strong></span>
      <span class="cf-footer-separator sm:hidden">&bull;</span>
      <span id="cf-footer-item-ip" class="cf-footer-item hidden sm:block sm:mb-1">
        Your IP:
        <button type="button" id="cf-footer-ip-reveal" class="cf-footer-ip-reveal-btn">Click to reveal</button>
        <span class="hidden" id="cf-footer-ip">**************</span>
        <span class="cf-footer-separator sm:hidden">&bull;</span>
      </span>
      <span class="cf-footer-item sm:block sm:mb-1"><span>Performance &amp; security by</span> <a rel="noopener noreferrer" href="https://www.cloudflare.com/5xx-error-landing?utm_source=errorcode_502&#38;utm_campaign=qbt.wikizell.com" id="brand_link" target="_blank">Cloudflare</a></span>
      
    </p>
    <script>(function(){function d(){var b=a.getElementById("cf-footer-item-ip"),c=a.getElementById("cf-footer-ip-reveal");b&&"classList"in b&&(b.classList.remove("hidden"),c.addEventListener("click",function(){c.classList.add("hidden");a.getElementById("cf-footer-ip").classList.remove("hidden")}))}var a=document;document.addEventListener&&a.addEventListener("DOMContentLoaded",d)})();</script>
  </div><!-- /.error-footer -->

    </div>
</div>
</body>
</html>
2025-06-23 12:00:05,885 - INFO - 127.0.0.1 - - [23/Jun/2025 12:00:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:00:15,159 - INFO - 127.0.0.1 - - [23/Jun/2025 12:00:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:00:15,742 - INFO - 127.0.0.1 - - [23/Jun/2025 12:00:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:00:27,206 - INFO - Checked 79 torrents. Paused: 0, Seeding: 76
2025-06-23 12:00:35,557 - INFO - 127.0.0.1 - - [23/Jun/2025 12:00:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:00:35,643 - INFO - 127.0.0.1 - - [23/Jun/2025 12:00:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:01:05,257 - INFO - 127.0.0.1 - - [23/Jun/2025 12:01:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:01:05,652 - INFO - 127.0.0.1 - - [23/Jun/2025 12:01:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:01:15,167 - INFO - 127.0.0.1 - - [23/Jun/2025 12:01:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:01:15,251 - INFO - 127.0.0.1 - - [23/Jun/2025 12:01:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:01:35,557 - INFO - 127.0.0.1 - - [23/Jun/2025 12:01:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:01:35,641 - INFO - 127.0.0.1 - - [23/Jun/2025 12:01:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:02:05,261 - INFO - 127.0.0.1 - - [23/Jun/2025 12:02:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:02:05,645 - INFO - 127.0.0.1 - - [23/Jun/2025 12:02:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:02:15,162 - INFO - 127.0.0.1 - - [23/Jun/2025 12:02:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:02:15,243 - INFO - 127.0.0.1 - - [23/Jun/2025 12:02:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:02:35,563 - INFO - 127.0.0.1 - - [23/Jun/2025 12:02:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:02:35,647 - INFO - 127.0.0.1 - - [23/Jun/2025 12:02:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:03:04,693 - INFO - Checked 79 torrents. Paused: 0, Seeding: 76
2025-06-23 12:03:05,253 - INFO - 127.0.0.1 - - [23/Jun/2025 12:03:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:03:05,640 - INFO - 127.0.0.1 - - [23/Jun/2025 12:03:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:03:15,157 - INFO - 127.0.0.1 - - [23/Jun/2025 12:03:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:03:15,242 - INFO - 127.0.0.1 - - [23/Jun/2025 12:03:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:03:35,557 - INFO - 127.0.0.1 - - [23/Jun/2025 12:03:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:03:35,647 - INFO - 127.0.0.1 - - [23/Jun/2025 12:03:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:04:05,250 - INFO - 127.0.0.1 - - [23/Jun/2025 12:04:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:04:05,642 - INFO - 127.0.0.1 - - [23/Jun/2025 12:04:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:04:15,177 - INFO - 127.0.0.1 - - [23/Jun/2025 12:04:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:04:15,269 - INFO - 127.0.0.1 - - [23/Jun/2025 12:04:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:04:35,563 - INFO - 127.0.0.1 - - [23/Jun/2025 12:04:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:04:35,646 - INFO - 127.0.0.1 - - [23/Jun/2025 12:04:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:05:05,253 - INFO - 127.0.0.1 - - [23/Jun/2025 12:05:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:05:05,643 - INFO - 127.0.0.1 - - [23/Jun/2025 12:05:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:05:15,160 - INFO - 127.0.0.1 - - [23/Jun/2025 12:05:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:05:15,238 - INFO - 127.0.0.1 - - [23/Jun/2025 12:05:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:05:27,506 - INFO - Checked 79 torrents. Paused: 0, Seeding: 76
2025-06-23 12:05:35,561 - INFO - 127.0.0.1 - - [23/Jun/2025 12:05:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:05:35,628 - INFO - 127.0.0.1 - - [23/Jun/2025 12:05:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:06:05,258 - INFO - 127.0.0.1 - - [23/Jun/2025 12:06:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:06:05,649 - INFO - 127.0.0.1 - - [23/Jun/2025 12:06:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:06:15,157 - INFO - 127.0.0.1 - - [23/Jun/2025 12:06:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:06:15,237 - INFO - 127.0.0.1 - - [23/Jun/2025 12:06:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:06:35,571 - INFO - 127.0.0.1 - - [23/Jun/2025 12:06:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:06:35,658 - INFO - 127.0.0.1 - - [23/Jun/2025 12:06:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:07:05,263 - INFO - 127.0.0.1 - - [23/Jun/2025 12:07:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:07:05,655 - INFO - 127.0.0.1 - - [23/Jun/2025 12:07:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:07:15,160 - INFO - 127.0.0.1 - - [23/Jun/2025 12:07:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:07:15,236 - INFO - 127.0.0.1 - - [23/Jun/2025 12:07:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:07:35,563 - INFO - 127.0.0.1 - - [23/Jun/2025 12:07:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:07:35,637 - INFO - 127.0.0.1 - - [23/Jun/2025 12:07:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:08:04,990 - INFO - Checked 79 torrents. Paused: 0, Seeding: 76
2025-06-23 12:08:05,250 - INFO - 127.0.0.1 - - [23/Jun/2025 12:08:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:08:05,907 - INFO - 127.0.0.1 - - [23/Jun/2025 12:08:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:08:15,153 - INFO - 127.0.0.1 - - [23/Jun/2025 12:08:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:08:15,232 - INFO - 127.0.0.1 - - [23/Jun/2025 12:08:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:08:35,563 - INFO - 127.0.0.1 - - [23/Jun/2025 12:08:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:08:35,654 - INFO - 127.0.0.1 - - [23/Jun/2025 12:08:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:09:05,250 - INFO - 127.0.0.1 - - [23/Jun/2025 12:09:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:09:05,632 - INFO - 127.0.0.1 - - [23/Jun/2025 12:09:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:09:15,156 - INFO - 127.0.0.1 - - [23/Jun/2025 12:09:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:09:15,243 - INFO - 127.0.0.1 - - [23/Jun/2025 12:09:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:09:35,561 - INFO - 127.0.0.1 - - [23/Jun/2025 12:09:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:09:35,647 - INFO - 127.0.0.1 - - [23/Jun/2025 12:09:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:10:05,559 - INFO - 127.0.0.1 - - [23/Jun/2025 12:10:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:10:05,650 - INFO - 127.0.0.1 - - [23/Jun/2025 12:10:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:10:15,160 - INFO - 127.0.0.1 - - [23/Jun/2025 12:10:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:10:15,243 - INFO - 127.0.0.1 - - [23/Jun/2025 12:10:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:10:27,810 - INFO - Checked 79 torrents. Paused: 0, Seeding: 76
2025-06-23 12:10:35,251 - INFO - 127.0.0.1 - - [23/Jun/2025 12:10:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:10:35,644 - INFO - 127.0.0.1 - - [23/Jun/2025 12:10:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:11:05,558 - INFO - 127.0.0.1 - - [23/Jun/2025 12:11:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:11:05,649 - INFO - 127.0.0.1 - - [23/Jun/2025 12:11:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:11:15,157 - INFO - 127.0.0.1 - - [23/Jun/2025 12:11:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:11:15,236 - INFO - 127.0.0.1 - - [23/Jun/2025 12:11:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:11:35,249 - INFO - 127.0.0.1 - - [23/Jun/2025 12:11:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:11:35,629 - INFO - 127.0.0.1 - - [23/Jun/2025 12:11:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:12:05,560 - INFO - 127.0.0.1 - - [23/Jun/2025 12:12:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:12:05,645 - INFO - 127.0.0.1 - - [23/Jun/2025 12:12:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:12:15,157 - INFO - 127.0.0.1 - - [23/Jun/2025 12:12:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:12:15,238 - INFO - 127.0.0.1 - - [23/Jun/2025 12:12:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:12:35,260 - INFO - 127.0.0.1 - - [23/Jun/2025 12:12:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:12:35,654 - INFO - 127.0.0.1 - - [23/Jun/2025 12:12:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:13:05,248 - INFO - Checked 79 torrents. Paused: 0, Seeding: 76
2025-06-23 12:13:05,569 - INFO - 127.0.0.1 - - [23/Jun/2025 12:13:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:13:05,652 - INFO - 127.0.0.1 - - [23/Jun/2025 12:13:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:13:15,167 - INFO - 127.0.0.1 - - [23/Jun/2025 12:13:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:13:15,250 - INFO - 127.0.0.1 - - [23/Jun/2025 12:13:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:13:35,262 - INFO - 127.0.0.1 - - [23/Jun/2025 12:13:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:13:35,633 - INFO - 127.0.0.1 - - [23/Jun/2025 12:13:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:14:05,561 - INFO - 127.0.0.1 - - [23/Jun/2025 12:14:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:14:05,646 - INFO - 127.0.0.1 - - [23/Jun/2025 12:14:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:14:15,163 - INFO - 127.0.0.1 - - [23/Jun/2025 12:14:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:14:15,267 - INFO - 127.0.0.1 - - [23/Jun/2025 12:14:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:14:35,263 - INFO - 127.0.0.1 - - [23/Jun/2025 12:14:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:14:35,637 - INFO - 127.0.0.1 - - [23/Jun/2025 12:14:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:15:05,561 - INFO - 127.0.0.1 - - [23/Jun/2025 12:15:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:15:05,638 - INFO - 127.0.0.1 - - [23/Jun/2025 12:15:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:15:15,161 - INFO - 127.0.0.1 - - [23/Jun/2025 12:15:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:15:15,239 - INFO - 127.0.0.1 - - [23/Jun/2025 12:15:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:15:28,093 - INFO - Checked 79 torrents. Paused: 0, Seeding: 76
2025-06-23 12:15:35,250 - INFO - 127.0.0.1 - - [23/Jun/2025 12:15:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:15:35,638 - INFO - 127.0.0.1 - - [23/Jun/2025 12:15:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:16:05,562 - INFO - 127.0.0.1 - - [23/Jun/2025 12:16:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:16:05,647 - INFO - 127.0.0.1 - - [23/Jun/2025 12:16:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:16:15,150 - INFO - 127.0.0.1 - - [23/Jun/2025 12:16:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:16:15,235 - INFO - 127.0.0.1 - - [23/Jun/2025 12:16:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:16:35,253 - INFO - 127.0.0.1 - - [23/Jun/2025 12:16:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:16:35,631 - INFO - 127.0.0.1 - - [23/Jun/2025 12:16:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:17:05,562 - INFO - 127.0.0.1 - - [23/Jun/2025 12:17:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:17:05,650 - INFO - 127.0.0.1 - - [23/Jun/2025 12:17:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:17:15,154 - INFO - 127.0.0.1 - - [23/Jun/2025 12:17:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:17:15,239 - INFO - 127.0.0.1 - - [23/Jun/2025 12:17:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:17:35,250 - INFO - 127.0.0.1 - - [23/Jun/2025 12:17:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:17:35,644 - INFO - 127.0.0.1 - - [23/Jun/2025 12:17:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:18:05,511 - INFO - Checked 79 torrents. Paused: 0, Seeding: 76
2025-06-23 12:18:05,563 - INFO - 127.0.0.1 - - [23/Jun/2025 12:18:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:18:05,641 - INFO - 127.0.0.1 - - [23/Jun/2025 12:18:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:18:15,153 - INFO - 127.0.0.1 - - [23/Jun/2025 12:18:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:18:15,247 - INFO - 127.0.0.1 - - [23/Jun/2025 12:18:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:18:35,258 - INFO - 127.0.0.1 - - [23/Jun/2025 12:18:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:18:35,639 - INFO - 127.0.0.1 - - [23/Jun/2025 12:18:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:19:05,570 - INFO - 127.0.0.1 - - [23/Jun/2025 12:19:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:19:05,655 - INFO - 127.0.0.1 - - [23/Jun/2025 12:19:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:19:15,156 - INFO - 127.0.0.1 - - [23/Jun/2025 12:19:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:19:15,235 - INFO - 127.0.0.1 - - [23/Jun/2025 12:19:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:19:35,260 - INFO - 127.0.0.1 - - [23/Jun/2025 12:19:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:19:35,647 - INFO - 127.0.0.1 - - [23/Jun/2025 12:19:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:20:05,561 - INFO - 127.0.0.1 - - [23/Jun/2025 12:20:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:20:05,648 - INFO - 127.0.0.1 - - [23/Jun/2025 12:20:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:20:15,163 - INFO - 127.0.0.1 - - [23/Jun/2025 12:20:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:20:15,245 - INFO - 127.0.0.1 - - [23/Jun/2025 12:20:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:20:28,375 - INFO - Checked 79 torrents. Paused: 0, Seeding: 76
2025-06-23 12:20:35,259 - INFO - 127.0.0.1 - - [23/Jun/2025 12:20:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:20:35,703 - INFO - 127.0.0.1 - - [23/Jun/2025 12:20:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:21:05,557 - INFO - 127.0.0.1 - - [23/Jun/2025 12:21:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:21:05,636 - INFO - 127.0.0.1 - - [23/Jun/2025 12:21:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:21:15,156 - INFO - 127.0.0.1 - - [23/Jun/2025 12:21:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:21:15,242 - INFO - 127.0.0.1 - - [23/Jun/2025 12:21:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:21:35,252 - INFO - 127.0.0.1 - - [23/Jun/2025 12:21:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:21:35,641 - INFO - 127.0.0.1 - - [23/Jun/2025 12:21:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:22:05,555 - INFO - 127.0.0.1 - - [23/Jun/2025 12:22:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:22:05,634 - INFO - 127.0.0.1 - - [23/Jun/2025 12:22:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:22:15,162 - INFO - 127.0.0.1 - - [23/Jun/2025 12:22:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:22:15,248 - INFO - 127.0.0.1 - - [23/Jun/2025 12:22:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:22:35,264 - INFO - 127.0.0.1 - - [23/Jun/2025 12:22:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:22:35,647 - INFO - 127.0.0.1 - - [23/Jun/2025 12:22:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:23:05,567 - INFO - 127.0.0.1 - - [23/Jun/2025 12:23:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:23:05,676 - INFO - 127.0.0.1 - - [23/Jun/2025 12:23:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:23:05,798 - INFO - Checked 79 torrents. Paused: 0, Seeding: 76
2025-06-23 12:23:15,155 - INFO - 127.0.0.1 - - [23/Jun/2025 12:23:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:23:15,234 - INFO - 127.0.0.1 - - [23/Jun/2025 12:23:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:23:35,248 - INFO - 127.0.0.1 - - [23/Jun/2025 12:23:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:23:35,657 - INFO - 127.0.0.1 - - [23/Jun/2025 12:23:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:24:05,562 - INFO - 127.0.0.1 - - [23/Jun/2025 12:24:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:24:05,651 - INFO - 127.0.0.1 - - [23/Jun/2025 12:24:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:24:15,151 - INFO - 127.0.0.1 - - [23/Jun/2025 12:24:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:24:15,302 - INFO - 127.0.0.1 - - [23/Jun/2025 12:24:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:24:35,259 - INFO - 127.0.0.1 - - [23/Jun/2025 12:24:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:24:35,649 - INFO - 127.0.0.1 - - [23/Jun/2025 12:24:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:25:05,558 - INFO - 127.0.0.1 - - [23/Jun/2025 12:25:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:25:05,643 - INFO - 127.0.0.1 - - [23/Jun/2025 12:25:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:25:15,162 - INFO - 127.0.0.1 - - [23/Jun/2025 12:25:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:25:15,236 - INFO - 127.0.0.1 - - [23/Jun/2025 12:25:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:25:28,679 - INFO - Checked 79 torrents. Paused: 0, Seeding: 76
2025-06-23 12:25:35,253 - INFO - 127.0.0.1 - - [23/Jun/2025 12:25:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:25:35,644 - INFO - 127.0.0.1 - - [23/Jun/2025 12:25:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:26:05,559 - INFO - 127.0.0.1 - - [23/Jun/2025 12:26:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:26:05,648 - INFO - 127.0.0.1 - - [23/Jun/2025 12:26:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:26:15,154 - INFO - 127.0.0.1 - - [23/Jun/2025 12:26:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:26:15,228 - INFO - 127.0.0.1 - - [23/Jun/2025 12:26:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:26:35,258 - INFO - 127.0.0.1 - - [23/Jun/2025 12:26:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:26:35,649 - INFO - 127.0.0.1 - - [23/Jun/2025 12:26:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:27:05,560 - INFO - 127.0.0.1 - - [23/Jun/2025 12:27:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:27:05,647 - INFO - 127.0.0.1 - - [23/Jun/2025 12:27:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:27:15,159 - INFO - 127.0.0.1 - - [23/Jun/2025 12:27:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:27:15,257 - INFO - 127.0.0.1 - - [23/Jun/2025 12:27:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:27:35,250 - INFO - 127.0.0.1 - - [23/Jun/2025 12:27:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:27:35,638 - INFO - 127.0.0.1 - - [23/Jun/2025 12:27:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:28:05,569 - INFO - 127.0.0.1 - - [23/Jun/2025 12:28:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:28:05,680 - INFO - 127.0.0.1 - - [23/Jun/2025 12:28:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:28:06,058 - INFO - Checked 79 torrents. Paused: 0, Seeding: 76
2025-06-23 12:28:15,159 - INFO - 127.0.0.1 - - [23/Jun/2025 12:28:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:28:15,243 - INFO - 127.0.0.1 - - [23/Jun/2025 12:28:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:28:35,259 - INFO - 127.0.0.1 - - [23/Jun/2025 12:28:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:28:35,650 - INFO - 127.0.0.1 - - [23/Jun/2025 12:28:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:29:05,567 - INFO - 127.0.0.1 - - [23/Jun/2025 12:29:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:29:05,650 - INFO - 127.0.0.1 - - [23/Jun/2025 12:29:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:29:15,166 - INFO - 127.0.0.1 - - [23/Jun/2025 12:29:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:29:15,261 - INFO - 127.0.0.1 - - [23/Jun/2025 12:29:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:29:35,248 - INFO - 127.0.0.1 - - [23/Jun/2025 12:29:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:29:35,632 - INFO - 127.0.0.1 - - [23/Jun/2025 12:29:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:30:05,570 - INFO - 127.0.0.1 - - [23/Jun/2025 12:30:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:30:05,650 - INFO - 127.0.0.1 - - [23/Jun/2025 12:30:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:30:15,163 - INFO - 127.0.0.1 - - [23/Jun/2025 12:30:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:30:15,243 - INFO - 127.0.0.1 - - [23/Jun/2025 12:30:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:30:28,953 - INFO - Checked 79 torrents. Paused: 0, Seeding: 76
2025-06-23 12:30:35,249 - INFO - 127.0.0.1 - - [23/Jun/2025 12:30:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:30:35,662 - INFO - 127.0.0.1 - - [23/Jun/2025 12:30:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:31:05,560 - INFO - 127.0.0.1 - - [23/Jun/2025 12:31:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:31:05,637 - INFO - 127.0.0.1 - - [23/Jun/2025 12:31:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:31:15,154 - INFO - 127.0.0.1 - - [23/Jun/2025 12:31:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:31:15,226 - INFO - 127.0.0.1 - - [23/Jun/2025 12:31:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:31:35,261 - INFO - 127.0.0.1 - - [23/Jun/2025 12:31:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:31:35,644 - INFO - 127.0.0.1 - - [23/Jun/2025 12:31:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:32:05,566 - INFO - 127.0.0.1 - - [23/Jun/2025 12:32:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:32:05,649 - INFO - 127.0.0.1 - - [23/Jun/2025 12:32:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:32:15,151 - INFO - 127.0.0.1 - - [23/Jun/2025 12:32:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:32:15,232 - INFO - 127.0.0.1 - - [23/Jun/2025 12:32:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:32:35,263 - INFO - 127.0.0.1 - - [23/Jun/2025 12:32:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:32:35,661 - INFO - 127.0.0.1 - - [23/Jun/2025 12:32:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:33:05,555 - INFO - 127.0.0.1 - - [23/Jun/2025 12:33:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:33:05,642 - INFO - 127.0.0.1 - - [23/Jun/2025 12:33:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:33:06,345 - INFO - Checked 79 torrents. Paused: 0, Seeding: 76
2025-06-23 12:33:15,154 - INFO - 127.0.0.1 - - [23/Jun/2025 12:33:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:33:15,232 - INFO - 127.0.0.1 - - [23/Jun/2025 12:33:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:33:35,259 - INFO - 127.0.0.1 - - [23/Jun/2025 12:33:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:33:35,643 - INFO - 127.0.0.1 - - [23/Jun/2025 12:33:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:34:05,560 - INFO - 127.0.0.1 - - [23/Jun/2025 12:34:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:34:05,643 - INFO - 127.0.0.1 - - [23/Jun/2025 12:34:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:34:15,153 - INFO - 127.0.0.1 - - [23/Jun/2025 12:34:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:34:15,235 - INFO - 127.0.0.1 - - [23/Jun/2025 12:34:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:34:35,254 - INFO - 127.0.0.1 - - [23/Jun/2025 12:34:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:34:35,643 - INFO - 127.0.0.1 - - [23/Jun/2025 12:34:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:35:05,567 - INFO - 127.0.0.1 - - [23/Jun/2025 12:35:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:35:05,647 - INFO - 127.0.0.1 - - [23/Jun/2025 12:35:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:35:15,154 - INFO - 127.0.0.1 - - [23/Jun/2025 12:35:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:35:15,236 - INFO - 127.0.0.1 - - [23/Jun/2025 12:35:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:35:29,240 - INFO - Checked 79 torrents. Paused: 0, Seeding: 76
2025-06-23 12:35:35,273 - INFO - 127.0.0.1 - - [23/Jun/2025 12:35:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:35:35,719 - INFO - 127.0.0.1 - - [23/Jun/2025 12:35:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:36:05,560 - INFO - 127.0.0.1 - - [23/Jun/2025 12:36:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:36:05,644 - INFO - 127.0.0.1 - - [23/Jun/2025 12:36:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:36:15,165 - INFO - 127.0.0.1 - - [23/Jun/2025 12:36:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:36:15,253 - INFO - 127.0.0.1 - - [23/Jun/2025 12:36:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:36:35,255 - INFO - 127.0.0.1 - - [23/Jun/2025 12:36:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:36:35,644 - INFO - 127.0.0.1 - - [23/Jun/2025 12:36:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:37:05,568 - INFO - 127.0.0.1 - - [23/Jun/2025 12:37:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:37:05,663 - INFO - 127.0.0.1 - - [23/Jun/2025 12:37:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:37:15,162 - INFO - 127.0.0.1 - - [23/Jun/2025 12:37:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:37:15,238 - INFO - 127.0.0.1 - - [23/Jun/2025 12:37:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:37:35,256 - INFO - 127.0.0.1 - - [23/Jun/2025 12:37:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:37:35,631 - INFO - 127.0.0.1 - - [23/Jun/2025 12:37:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:38:05,572 - INFO - 127.0.0.1 - - [23/Jun/2025 12:38:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:38:05,724 - INFO - 127.0.0.1 - - [23/Jun/2025 12:38:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:38:06,652 - INFO - Checked 79 torrents. Paused: 0, Seeding: 76
2025-06-23 12:38:15,164 - INFO - 127.0.0.1 - - [23/Jun/2025 12:38:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:38:15,234 - INFO - 127.0.0.1 - - [23/Jun/2025 12:38:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:38:35,255 - INFO - 127.0.0.1 - - [23/Jun/2025 12:38:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:38:35,648 - INFO - 127.0.0.1 - - [23/Jun/2025 12:38:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:39:05,573 - INFO - 127.0.0.1 - - [23/Jun/2025 12:39:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:39:05,655 - INFO - 127.0.0.1 - - [23/Jun/2025 12:39:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:39:15,166 - INFO - 127.0.0.1 - - [23/Jun/2025 12:39:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:39:15,249 - INFO - 127.0.0.1 - - [23/Jun/2025 12:39:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:39:35,255 - INFO - 127.0.0.1 - - [23/Jun/2025 12:39:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:39:35,638 - INFO - 127.0.0.1 - - [23/Jun/2025 12:39:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:40:05,567 - INFO - 127.0.0.1 - - [23/Jun/2025 12:40:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:40:05,650 - INFO - 127.0.0.1 - - [23/Jun/2025 12:40:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:40:15,157 - INFO - 127.0.0.1 - - [23/Jun/2025 12:40:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:40:15,237 - INFO - 127.0.0.1 - - [23/Jun/2025 12:40:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:40:29,584 - INFO - Checked 79 torrents. Paused: 0, Seeding: 76
2025-06-23 12:40:35,253 - INFO - 127.0.0.1 - - [23/Jun/2025 12:40:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:40:35,630 - INFO - 127.0.0.1 - - [23/Jun/2025 12:40:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:41:05,564 - INFO - 127.0.0.1 - - [23/Jun/2025 12:41:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:41:05,651 - INFO - 127.0.0.1 - - [23/Jun/2025 12:41:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:41:15,156 - INFO - 127.0.0.1 - - [23/Jun/2025 12:41:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:41:15,232 - INFO - 127.0.0.1 - - [23/Jun/2025 12:41:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:41:35,251 - INFO - 127.0.0.1 - - [23/Jun/2025 12:41:35] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:41:35,640 - INFO - 127.0.0.1 - - [23/Jun/2025 12:41:35] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:42:05,567 - INFO - 127.0.0.1 - - [23/Jun/2025 12:42:05] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:42:05,657 - INFO - 127.0.0.1 - - [23/Jun/2025 12:42:05] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:42:15,171 - INFO - 127.0.0.1 - - [23/Jun/2025 12:42:15] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:42:15,259 - INFO - 127.0.0.1 - - [23/Jun/2025 12:42:15] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:42:19,980 - INFO - 127.0.0.1 - - [23/Jun/2025 12:42:19] "GET / HTTP/1.1" 200 -
2025-06-23 12:42:20,294 - INFO - 127.0.0.1 - - [23/Jun/2025 12:42:20] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-23 12:42:20,623 - INFO - 127.0.0.1 - - [23/Jun/2025 12:42:20] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:42:20,710 - INFO - 127.0.0.1 - - [23/Jun/2025 12:42:20] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:42:20,714 - INFO - 127.0.0.1 - - [23/Jun/2025 12:42:20] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 204 -
2025-06-23 12:42:51,431 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 12:42:51,490 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-23 12:42:51,491 - INFO - [33mPress CTRL+C to quit[0m
2025-06-23 12:42:51,493 - INFO -  * Restarting with stat
2025-06-23 12:42:52,432 - INFO - Connected to qBittorrent at qbt.wikizell.com:443
2025-06-23 12:42:52,480 - WARNING -  * Debugger is active!
2025-06-23 12:42:52,483 - INFO -  * Debugger PIN: 634-283-480
2025-06-23 12:42:58,585 - INFO - 127.0.0.1 - - [23/Jun/2025 12:42:58] "GET / HTTP/1.1" 200 -
2025-06-23 12:42:58,659 - INFO - 127.0.0.1 - - [23/Jun/2025 12:42:58] "GET /static/js/app.js HTTP/1.1" 200 -
2025-06-23 12:42:58,918 - INFO - 127.0.0.1 - - [23/Jun/2025 12:42:58] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:42:58,986 - INFO - 127.0.0.1 - - [23/Jun/2025 12:42:58] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:42:59,277 - INFO - 127.0.0.1 - - [23/Jun/2025 12:42:59] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 204 -
2025-06-23 12:43:03,863 - INFO - 127.0.0.1 - - [23/Jun/2025 12:43:03] "GET / HTTP/1.1" 200 -
2025-06-23 12:43:03,929 - INFO - 127.0.0.1 - - [23/Jun/2025 12:43:03] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-23 12:43:03,955 - INFO - 127.0.0.1 - - [23/Jun/2025 12:43:03] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:43:03,994 - INFO - 127.0.0.1 - - [23/Jun/2025 12:43:03] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 204 -
2025-06-23 12:43:04,046 - INFO - 127.0.0.1 - - [23/Jun/2025 12:43:04] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:43:34,155 - INFO - 127.0.0.1 - - [23/Jun/2025 12:43:34] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:43:34,234 - INFO - 127.0.0.1 - - [23/Jun/2025 12:43:34] "GET /api/torrents HTTP/1.1" 200 -
2025-06-23 12:44:04,153 - INFO - 127.0.0.1 - - [23/Jun/2025 12:44:04] "GET /api/status HTTP/1.1" 200 -
2025-06-23 12:44:04,235 - INFO - 127.0.0.1 - - [23/Jun/2025 12:44:04] "GET /api/torrents HTTP/1.1" 200 -
