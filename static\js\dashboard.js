class Dashboard {
    constructor() {
        this.apiBase = '/api';
        this.darkMode = localStorage.getItem('darkMode') === 'true';
        this.stateChart = null;
        this.uploadChart = null;
        
        this.init();
    }
    
    init() {
        this.setupDarkMode();
        this.loadDashboardData();
        this.setupEventListeners();
        this.startAutoRefresh();
    }
    
    setupDarkMode() {
        if (this.darkMode) {
            document.documentElement.setAttribute('data-theme', 'dark');
            document.getElementById('darkModeToggle').innerHTML = '<i class="bi bi-sun"></i>';
        } else {
            document.documentElement.setAttribute('data-theme', 'light');
            document.getElementById('darkModeToggle').innerHTML = '<i class="bi bi-moon"></i>';
        }
    }
    
    setupEventListeners() {
        document.getElementById('darkModeToggle').addEventListener('click', () => {
            this.toggleDarkMode();
        });
    }
    
    toggleDarkMode() {
        this.darkMode = !this.darkMode;
        document.documentElement.setAttribute('data-theme', this.darkMode ? 'dark' : 'light');
        localStorage.setItem('darkMode', this.darkMode);

        const icon = this.darkMode ? 'bi-sun' : 'bi-moon';
        document.getElementById('darkModeToggle').innerHTML = `<i class="bi ${icon}"></i>`;
    }
    
    async apiCall(endpoint, method = 'GET', data = null) {
        try {
            const options = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                }
            };
            
            if (data) {
                options.body = JSON.stringify(data);
            }
            
            const response = await fetch(this.apiBase + endpoint, options);
            return await response.json();
        } catch (error) {
            console.error('API call failed:', error);
            return null;
        }
    }
    
    async loadDashboardData() {
        await this.loadStatus();
        await this.loadTorrents();
        await this.loadActivity();
    }
    
    async loadStatus() {
        const status = await this.apiCall('/status');
        if (status && status.connected) {
            document.getElementById('connectionStatus').className = 'status-indicator status-connected';
            document.getElementById('connectionText').textContent = 'Connected';
            
            // Update metrics
            const stats = status.stats;
            document.getElementById('totalTorrentsMetric').textContent = stats.total_torrents;
            document.getElementById('activeSeedersMetric').textContent = stats.seeding_torrents;
            document.getElementById('totalPeersMetric').textContent = stats.total_peers;
            document.getElementById('avgRatioMetric').textContent = stats.avg_ratio.toFixed(2);
        } else {
            document.getElementById('connectionStatus').className = 'status-indicator status-disconnected';
            document.getElementById('connectionText').textContent = 'Disconnected';
        }
    }
    
    async loadTorrents() {
        const torrents = await this.apiCall('/torrents');
        if (torrents) {
            this.updateStateChart(torrents);
            this.updateTopTorrents(torrents);
            this.updateUploadChart(torrents);
        }
    }
    
    async loadActivity() {
        const response = await this.apiCall('/activity');
        if (response && response.success) {
            // Convert timestamps to relative time
            const activities = response.activities.map(activity => ({
                action: activity.action,
                torrent: `${activity.torrent_name} ${activity.details ? `(${activity.details})` : ''}`,
                time: this.getRelativeTime(activity.timestamp),
                type: this.getActivityType(activity.action)
            }));

            this.updateRecentActivity(activities.slice(0, 10)); // Show last 10 activities
        }
    }
    
    updateStateChart(torrents) {
        const states = {};
        torrents.forEach(torrent => {
            const state = this.formatState(torrent.state);
            states[state] = (states[state] || 0) + 1;
        });
        
        const ctx = document.getElementById('stateChart').getContext('2d');
        
        if (this.stateChart) {
            this.stateChart.destroy();
        }
        
        this.stateChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: Object.keys(states),
                datasets: [{
                    data: Object.values(states),
                    backgroundColor: [
                        '#28a745', // Seeding
                        '#ffc107', // Stalled
                        '#6c757d', // Paused
                        '#17a2b8', // Queued
                        '#dc3545', // Error
                        '#007bff'  // Other
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
    
    updateUploadChart(torrents) {
        // Simulate upload activity data (in a real app, you'd track this over time)
        const hours = [];
        const uploadData = [];
        
        for (let i = 23; i >= 0; i--) {
            const hour = new Date();
            hour.setHours(hour.getHours() - i);
            hours.push(hour.getHours() + ':00');
            
            // Simulate some upload activity
            const activeUploads = torrents.filter(t => t.upspeed > 0).length;
            uploadData.push(Math.random() * activeUploads * 10);
        }
        
        const ctx = document.getElementById('uploadChart').getContext('2d');
        
        if (this.uploadChart) {
            this.uploadChart.destroy();
        }
        
        this.uploadChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: hours,
                datasets: [{
                    label: 'Upload Speed (MB/s)',
                    data: uploadData,
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }
    
    updateTopTorrents(torrents) {
        // Top torrents by ratio
        const topRatio = [...torrents]
            .sort((a, b) => b.ratio - a.ratio)
            .slice(0, 5);
        
        const ratioHtml = topRatio.map(torrent => `
            <div class="top-torrent">
                <div class="torrent-name" title="${torrent.name}">
                    ${torrent.name.length > 40 ? torrent.name.substring(0, 40) + '...' : torrent.name}
                </div>
                <div class="torrent-metric">${torrent.ratio.toFixed(2)}</div>
            </div>
        `).join('');
        
        document.getElementById('topRatioTorrents').innerHTML = ratioHtml;
        
        // Most active uploads
        const topUploads = [...torrents]
            .filter(t => t.upspeed > 0)
            .sort((a, b) => b.upspeed - a.upspeed)
            .slice(0, 5);
        
        const uploadHtml = topUploads.length > 0 ? topUploads.map(torrent => `
            <div class="top-torrent">
                <div class="torrent-name" title="${torrent.name}">
                    ${torrent.name.length > 40 ? torrent.name.substring(0, 40) + '...' : torrent.name}
                </div>
                <div class="torrent-metric">${this.formatSpeed(torrent.upspeed)}</div>
            </div>
        `).join('') : '<div class="text-center text-muted">No active uploads</div>';
        
        document.getElementById('topUploadTorrents').innerHTML = uploadHtml;
        
        // Most popular (by peers)
        const topPeers = [...torrents]
            .filter(t => t.num_leechs > 0)
            .sort((a, b) => b.num_leechs - a.num_leechs)
            .slice(0, 5);
        
        const peersHtml = topPeers.length > 0 ? topPeers.map(torrent => `
            <div class="top-torrent">
                <div class="torrent-name" title="${torrent.name}">
                    ${torrent.name.length > 40 ? torrent.name.substring(0, 40) + '...' : torrent.name}
                </div>
                <div class="torrent-metric">${torrent.num_leechs} peers</div>
            </div>
        `).join('') : '<div class="text-center text-muted">No active downloads</div>';
        
        document.getElementById('topPeerTorrents').innerHTML = peersHtml;
    }
    
    updateRecentActivity(activities) {
        const activityHtml = activities.map(activity => {
            const iconMap = {
                'pause': 'bi-pause-circle text-warning',
                'resume': 'bi-play-circle text-success',
                'force-start': 'bi-lightning-circle text-primary',
                'auto-pause': 'bi-gear text-info'
            };
            
            return `
                <div class="activity-item">
                    <div class="d-flex align-items-center">
                        <i class="bi ${iconMap[activity.type]} me-2"></i>
                        <div>
                            <strong>${activity.action}</strong> ${activity.torrent}
                            <div class="activity-time">${activity.time}</div>
                        </div>
                    </div>
                </div>
            `;
        }).join('');
        
        document.getElementById('recentActivity').innerHTML = activityHtml;
    }
    
    formatState(state) {
        const stateMap = {
            'uploading': 'Seeding',
            'forcedUP': 'Force Seeding',
            'pausedUP': 'Paused',
            'stalledUP': 'Stalled',
            'stoppedUP': 'Stopped',
            'checkingUP': 'Checking',
            'downloading': 'Downloading',
            'pausedDL': 'Paused',
            'error': 'Error',
            'queuedUP': 'Queued',
            'queuedDL': 'Queued'
        };
        return stateMap[state] || state;
    }
    
    formatSpeed(bytesPerSecond) {
        if (bytesPerSecond === 0) return '0 B/s';
        const k = 1024;
        const sizes = ['B/s', 'KB/s', 'MB/s', 'GB/s'];
        const i = Math.floor(Math.log(bytesPerSecond) / Math.log(k));
        return parseFloat((bytesPerSecond / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }
    
    getRelativeTime(timestamp) {
        const now = new Date();
        const time = new Date(timestamp);
        const diffMs = now - time;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMs / 3600000);
        const diffDays = Math.floor(diffMs / 86400000);

        if (diffMins < 1) return 'Just now';
        if (diffMins < 60) return `${diffMins} minute${diffMins > 1 ? 's' : ''} ago`;
        if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
        return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    }

    getActivityType(action) {
        const typeMap = {
            'Paused': 'pause',
            'Resumed': 'resume',
            'Force Started': 'force-start',
            'Auto Paused': 'auto-pause'
        };
        return typeMap[action] || 'other';
    }

    startAutoRefresh() {
        setInterval(() => {
            this.loadDashboardData();
        }, 30000); // Refresh every 30 seconds
    }
}

// Initialize the dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.dashboard = new Dashboard();
});
