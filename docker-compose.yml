version: '3.8'

services:
  seed-manager:
    build: .
    container_name: qbt-seed-manager
    ports:
      - "5000:5000"
    volumes:
      - ./config.json:/app/config.json
      - ./logs:/app/logs
    environment:
      - FLASK_ENV=production
      - QB_HOST=${QB_HOST:-localhost}
      - QB_PORT=${QB_PORT:-8080}
      - QB_USERNAME=${QB_USERNAME:-admin}
      - QB_PASSWORD=${QB_PASSWORD:-adminpass}
      - SEED_LIMIT=${SEED_LIMIT:-5}
      - MIN_RATIO=${MIN_RATIO:-2.0}
      - CATEGORY=${CATEGORY:-movies}
      - CHECK_INTERVAL=${CHECK_INTERVAL:-300}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
