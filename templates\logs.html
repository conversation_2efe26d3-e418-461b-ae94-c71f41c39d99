<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Activity Logs - qBittorrent Seed Manager</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    
    <style>
        .log-entry {
            border-left: 4px solid var(--color-blue);
            background-color: var(--color-tertiary-system-grouped-background);
            padding: 1rem;
            margin-bottom: 0.5rem;
            border-radius: 0 8px 8px 0;
            transition: all 0.2s ease;
        }
        
        .log-entry:hover {
            background-color: var(--color-quaternary-system-fill);
            transform: translateX(2px);
        }
        
        .log-entry.temp-started {
            border-left-color: var(--color-orange);
        }
        
        .log-entry.auto-paused {
            border-left-color: var(--color-red);
        }
        
        .log-entry.resumed {
            border-left-color: var(--color-green);
        }
        
        .log-entry.force-started {
            border-left-color: var(--color-purple);
        }
        
        .log-timestamp {
            font-size: 0.85rem;
            color: var(--color-secondary-label);
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }
        
        .log-action {
            font-weight: 600;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            display: inline-block;
            margin-right: 0.5rem;
        }
        
        .log-action.temp-started {
            background-color: rgba(255, 149, 0, 0.2);
            color: var(--color-orange);
        }
        
        .log-action.auto-paused {
            background-color: rgba(255, 59, 48, 0.2);
            color: var(--color-red);
        }
        
        .log-action.resumed {
            background-color: rgba(52, 199, 89, 0.2);
            color: var(--color-green);
        }
        
        .log-action.force-started {
            background-color: rgba(175, 82, 222, 0.2);
            color: var(--color-purple);
        }
        
        .log-action.paused {
            background-color: rgba(255, 204, 0, 0.2);
            color: var(--color-yellow);
        }
        
        .log-torrent-name {
            font-weight: 500;
            color: var(--color-label);
            word-break: break-word;
        }
        
        .log-details {
            font-size: 0.9rem;
            color: var(--color-secondary-label);
            margin-top: 0.25rem;
        }
        
        .logs-header {
            background: linear-gradient(135deg, var(--color-blue), var(--color-purple));
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 0 0 20px 20px;
        }
        
        .auto-refresh-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            color: var(--color-secondary-label);
        }
        
        .refresh-dot {
            width: 8px;
            height: 8px;
            background-color: var(--color-green);
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .empty-logs {
            text-align: center;
            padding: 3rem;
            color: var(--color-secondary-label);
        }
        
        .filter-controls {
            background-color: var(--color-secondary-system-grouped-background);
            padding: 1rem;
            border-radius: 12px;
            margin-bottom: 1.5rem;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-cloud-download"></i>
                qBittorrent Seed Manager
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link me-3" href="/">
                    <i class="bi bi-list-ul"></i> Torrents
                </a>
                <a class="nav-link me-3" href="/dashboard">
                    <i class="bi bi-graph-up"></i> Dashboard
                </a>
                <a class="nav-link me-3" href="/info">
                    <i class="bi bi-info-circle"></i> Info
                </a>
                <button class="btn btn-outline-light btn-sm me-2" id="darkModeToggle">
                    <i class="bi bi-moon"></i>
                </button>
                <span class="navbar-text">
                    <span class="status-indicator" id="connectionStatus"></span>
                    <span id="connectionText">Disconnected</span>
                </span>
            </div>
        </div>
    </nav>

    <div class="logs-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="bi bi-journal-text"></i> Activity Logs
                    </h1>
                    <p class="mb-0 opacity-75">Real-time monitoring of torrent management activities</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="auto-refresh-indicator">
                        <span class="refresh-dot"></span>
                        Auto-refreshing every 5 seconds
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Filter Controls -->
        <div class="filter-controls">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <label for="actionFilter" class="form-label mb-1">Filter by Action:</label>
                    <select class="form-select form-select-sm" id="actionFilter">
                        <option value="">All Actions</option>
                        <option value="Temp Started">Temporary Starts</option>
                        <option value="Auto Paused">Auto Paused</option>
                        <option value="Resumed">Resumed</option>
                        <option value="Paused">Manual Paused</option>
                        <option value="Force Started">Force Started</option>
                    </select>
                </div>
                <div class="col-md-6">
                    <label for="torrentFilter" class="form-label mb-1">Filter by Torrent Name:</label>
                    <input type="text" class="form-control form-control-sm" id="torrentFilter" placeholder="Search torrent names...">
                </div>
            </div>
        </div>

        <!-- Logs Container -->
        <div id="logsContainer">
            <div class="empty-logs">
                <i class="bi bi-hourglass-split" style="font-size: 3rem; opacity: 0.5;"></i>
                <h5 class="mt-3">Loading activity logs...</h5>
                <p>Please wait while we fetch the latest activity data.</p>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Logs JavaScript -->
    <script>
        class LogsManager {
            constructor() {
                this.apiBase = '/api';
                this.refreshInterval = null;
                this.darkMode = localStorage.getItem('darkMode') === 'true';
                this.allLogs = [];
                this.filteredLogs = [];
                
                this.init();
            }
            
            init() {
                this.setupEventListeners();
                this.applyDarkMode();
                this.loadLogs();
                this.startAutoRefresh();
            }
            
            setupEventListeners() {
                // Dark mode toggle
                document.getElementById('darkModeToggle').addEventListener('click', () => {
                    this.toggleDarkMode();
                });
                
                // Filter controls
                document.getElementById('actionFilter').addEventListener('change', () => {
                    this.applyFilters();
                });
                
                document.getElementById('torrentFilter').addEventListener('input', () => {
                    this.applyFilters();
                });
            }
            
            applyDarkMode() {
                if (this.darkMode) {
                    document.documentElement.setAttribute('data-theme', 'dark');
                    document.getElementById('darkModeToggle').innerHTML = '<i class="bi bi-sun"></i>';
                }
            }
            
            toggleDarkMode() {
                this.darkMode = !this.darkMode;
                localStorage.setItem('darkMode', this.darkMode);
                document.documentElement.setAttribute('data-theme', this.darkMode ? 'dark' : 'light');
                document.getElementById('darkModeToggle').innerHTML = this.darkMode ? '<i class="bi bi-sun"></i>' : '<i class="bi bi-moon"></i>';
            }
            
            async apiCall(endpoint, method = 'GET', data = null) {
                try {
                    const options = {
                        method: method,
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    };
                    
                    if (data) {
                        options.body = JSON.stringify(data);
                    }
                    
                    const response = await fetch(this.apiBase + endpoint, options);
                    return await response.json();
                } catch (error) {
                    console.error('API call failed:', error);
                    return null;
                }
            }
            
            async loadLogs() {
                const result = await this.apiCall('/activity');
                if (result && result.success) {
                    this.allLogs = result.activities || [];
                    this.applyFilters();
                }
            }
            
            applyFilters() {
                const actionFilter = document.getElementById('actionFilter').value;
                const torrentFilter = document.getElementById('torrentFilter').value.toLowerCase();
                
                this.filteredLogs = this.allLogs.filter(log => {
                    // Action filter
                    if (actionFilter && log.action !== actionFilter) {
                        return false;
                    }
                    
                    // Torrent name filter
                    if (torrentFilter && !log.torrent_name.toLowerCase().includes(torrentFilter)) {
                        return false;
                    }
                    
                    return true;
                });
                
                this.updateLogsDisplay();
            }
            
            updateLogsDisplay() {
                const container = document.getElementById('logsContainer');
                
                if (this.filteredLogs.length === 0) {
                    container.innerHTML = `
                        <div class="empty-logs">
                            <i class="bi bi-journal-x" style="font-size: 3rem; opacity: 0.5;"></i>
                            <h5 class="mt-3">No activity logs found</h5>
                            <p>No management activities match your current filters.</p>
                        </div>
                    `;
                    return;
                }
                
                const logsHtml = this.filteredLogs.map(log => {
                    const timestamp = new Date(log.timestamp).toLocaleString();
                    const actionClass = log.action.toLowerCase().replace(/\s+/g, '-');
                    
                    return `
                        <div class="log-entry ${actionClass}">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <div>
                                    <span class="log-action ${actionClass}">${log.action}</span>
                                    <span class="log-timestamp">${timestamp}</span>
                                </div>
                            </div>
                            <div class="log-torrent-name">${log.torrent_name}</div>
                            ${log.details ? `<div class="log-details">${log.details}</div>` : ''}
                        </div>
                    `;
                }).join('');
                
                container.innerHTML = logsHtml;
            }
            
            startAutoRefresh() {
                this.refreshInterval = setInterval(() => {
                    this.loadLogs();
                }, 5000); // Refresh every 5 seconds
            }
            
            stopAutoRefresh() {
                if (this.refreshInterval) {
                    clearInterval(this.refreshInterval);
                }
            }
        }
        
        // Initialize the logs manager when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            window.logsManager = new LogsManager();
        });
    </script>
</body>
</html>
