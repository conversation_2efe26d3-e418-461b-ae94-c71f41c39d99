<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Documentation - qBittorrent Seed Manager</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    
    <style>
        .doc-section {
            margin-bottom: 2rem;
        }
        
        .doc-header {
            border-bottom: 2px solid var(--color-blue);
            padding-bottom: 0.5rem;
            margin-bottom: 1rem;
            color: var(--color-blue);
        }
        
        .feature-card {
            border-left: 4px solid var(--color-blue);
            background-color: var(--color-tertiary-system-grouped-background);
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 0 8px 8px 0;
        }
        
        .logic-step {
            background-color: var(--color-secondary-system-fill);
            padding: 0.75rem;
            margin: 0.5rem 0;
            border-radius: 8px;
            border-left: 3px solid var(--color-green);
        }
        
        .warning-box {
            background-color: rgba(255, 149, 0, 0.1);
            border: 1px solid var(--color-orange);
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .code-block {
            background-color: var(--color-quaternary-system-fill);
            border: 1px solid var(--color-separator);
            border-radius: 8px;
            padding: 1rem;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9rem;
        }
        
        .toc {
            background-color: var(--color-tertiary-system-grouped-background);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .toc ul {
            list-style: none;
            padding-left: 0;
        }
        
        .toc li {
            padding: 0.25rem 0;
        }
        
        .toc a {
            color: var(--color-blue);
            text-decoration: none;
        }
        
        .toc a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-cloud-download"></i>
                qBittorrent Seed Manager
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link me-3" href="/">
                    <i class="bi bi-list-ul"></i> Torrents
                </a>
                <a class="nav-link me-3" href="/dashboard">
                    <i class="bi bi-graph-up"></i> Dashboard
                </a>
                <a class="nav-link me-3" href="/logs">
                    <i class="bi bi-journal-text"></i> Logs
                </a>
                <button class="btn btn-outline-light btn-sm me-2" id="darkModeToggle">
                    <i class="bi bi-moon"></i>
                </button>
                <span class="navbar-text">
                    <span class="status-indicator" id="connectionStatus"></span>
                    <span id="connectionText">Disconnected</span>
                </span>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-3">
                <!-- Table of Contents -->
                <div class="toc sticky-top" style="top: 2rem;">
                    <h5 class="mb-3"><i class="bi bi-list"></i> Table of Contents</h5>
                    <ul>
                        <li><a href="#overview">Overview</a></li>
                        <li><a href="#features">Key Features</a></li>
                        <li><a href="#logic">Management Logic</a></li>
                        <li><a href="#interface">Interface Guide</a></li>
                        <li><a href="#configuration">Configuration</a></li>
                        <li><a href="#troubleshooting">Troubleshooting</a></li>
                        <li><a href="#api">API Reference</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="col-md-9">
                <!-- Overview Section -->
                <section id="overview" class="doc-section">
                    <h2 class="doc-header"><i class="bi bi-info-circle"></i> Overview</h2>
                    <p class="lead">
                        qBittorrent Seed Manager is an intelligent automation tool designed to optimize your seeding strategy 
                        by automatically managing torrents based on configurable criteria.
                    </p>
                    
                    <div class="feature-card">
                        <h5><i class="bi bi-gear"></i> Intelligent Automation</h5>
                        <p>Automatically pauses torrents when they reach optimal seeding conditions, helping you maintain 
                        a healthy ratio while managing bandwidth and storage efficiently.</p>
                    </div>
                    
                    <div class="feature-card">
                        <h5><i class="bi bi-eye"></i> Real-time Monitoring</h5>
                        <p>Provides comprehensive visibility into your torrent ecosystem with live statistics, 
                        peer connections, and upload activity tracking.</p>
                    </div>
                </section>

                <!-- Features Section -->
                <section id="features" class="doc-section">
                    <h2 class="doc-header"><i class="bi bi-star"></i> Key Features</h2>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h5><i class="bi bi-table"></i> Advanced Table Management</h5>
                            <ul>
                                <li><strong>Sortable Columns:</strong> Click any header to sort by that field</li>
                                <li><strong>Advanced Filtering:</strong> Filter by name, state, ratio, and peer activity</li>
                                <li><strong>qBittorrent-Style Display:</strong> Familiar interface matching qBittorrent</li>
                                <li><strong>Real-time Updates:</strong> Data refreshes every 30 seconds</li>
                            </ul>
                        </div>
                        
                        <div class="col-md-6">
                            <h5><i class="bi bi-graph-up"></i> Analytics Dashboard</h5>
                            <ul>
                                <li><strong>State Distribution:</strong> Visual breakdown of torrent states</li>
                                <li><strong>Upload Activity:</strong> 24-hour activity charts</li>
                                <li><strong>Top Performers:</strong> Highest ratio and most active torrents</li>
                                <li><strong>Management History:</strong> Track all automated and manual actions</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <h5><i class="bi bi-palette"></i> Apple Design System</h5>
                            <ul>
                                <li><strong>Human Interface Guidelines:</strong> Apple's color system</li>
                                <li><strong>Dark/Light Themes:</strong> Seamless theme switching</li>
                                <li><strong>Smooth Transitions:</strong> Polished animations and effects</li>
                                <li><strong>Accessibility:</strong> High contrast and clear focus states</li>
                            </ul>
                        </div>
                        
                        <div class="col-md-6">
                            <h5><i class="bi bi-lightning"></i> Torrent Control</h5>
                            <ul>
                                <li><strong>Individual Actions:</strong> Resume, pause, force check, force start</li>
                                <li><strong>Bulk Operations:</strong> Force check all torrents</li>
                                <li><strong>Category Management:</strong> Switch between categories quickly</li>
                                <li><strong>Force Start:</strong> Override queue limits for priority torrents</li>
                            </ul>
                        </div>
                    </div>
                </section>

                <!-- Logic Section -->
                <section id="logic" class="doc-section">
                    <h2 class="doc-header"><i class="bi bi-cpu"></i> Management Logic</h2>
                    
                    <h5>Intelligent Seed Count Detection</h5>
                    <p>The seed manager uses an advanced algorithm to get accurate seed counts before making decisions:</p>

                    <div class="logic-step">
                        <strong>Step 1: Identify Candidates</strong><br>
                        Find torrents that meet ratio requirements and might need seed count verification
                    </div>

                    <div class="logic-step">
                        <strong>Step 2: Temporary Activation</strong><br>
                        Temporarily start completed/paused/stopped torrents to connect to trackers and get fresh peer data
                    </div>

                    <div class="logic-step">
                        <strong>Step 3: Tracker Update Wait</strong><br>
                        Wait 15 seconds for torrents to connect and receive updated seed/peer information
                    </div>

                    <div class="logic-step">
                        <strong>Step 4: Seed Count Check</strong><br>
                        Torrent must have <code>≥ Seed Limit</code> other seeders available (using fresh data)
                    </div>

                    <div class="logic-step">
                        <strong>Step 5: Ratio Check</strong><br>
                        Torrent must have achieved <code>≥ Minimum Ratio</code>
                    </div>

                    <div class="logic-step">
                        <strong>Step 6: Auto-Stop</strong><br>
                        If both conditions are met, torrent is automatically stopped
                    </div>
                    
                    <div class="warning-box">
                        <h6><i class="bi bi-exclamation-triangle"></i> Important Notes</h6>
                        <ul class="mb-0">
                            <li><strong>Force Started torrents</strong> are never auto-stopped</li>
                            <li><strong>Both conditions</strong> must be met simultaneously</li>
                            <li><strong>Manual actions</strong> always override automatic behavior</li>
                            <li><strong>Category filtering</strong> ensures only specified torrents are managed</li>
                        </ul>
                    </div>
                    
                    <h5 class="mt-4">Ratio Display Logic</h5>
                    <div class="logic-step">
                        <strong>Green Badge:</strong> Ratio ≥ 2.0 (Excellent seeding performance)
                    </div>
                    <div class="logic-step">
                        <strong>Plain Number:</strong> Ratio < 2.0 (Still building ratio)
                    </div>
                </section>

                <!-- Interface Guide Section -->
                <section id="interface" class="doc-section">
                    <h2 class="doc-header"><i class="bi bi-display"></i> Interface Guide</h2>

                    <h5>Main Torrents Table</h5>
                    <div class="code-block">
                        <strong>Seeds Column Format:</strong> Connected(Total)<br>
                        <strong>Peers Column Format:</strong> Connected(Total)<br>
                        <strong>Ratio Display:</strong> Green badge for ≥2.0, plain number for <2.0
                    </div>

                    <h5 class="mt-3">Filtering Options</h5>
                    <ul>
                        <li><strong>Name Filter:</strong> Search torrents by name (case-insensitive)</li>
                        <li><strong>State Filter:</strong> Filter by torrent state (seeding, paused, etc.)</li>
                        <li><strong>Ratio Range:</strong> Set minimum and maximum ratio bounds</li>
                        <li><strong>Peer Activity:</strong> Show only torrents with/without active peers</li>
                    </ul>

                    <div class="warning-box">
                        <h6><i class="bi bi-info-circle"></i> Filter Persistence</h6>
                        <p class="mb-0">Filters and sorting are preserved when data refreshes, ensuring your view remains consistent during automatic updates.</p>
                    </div>
                </section>

                <!-- Configuration Section -->
                <section id="configuration" class="doc-section">
                    <h2 class="doc-header"><i class="bi bi-gear"></i> Configuration</h2>

                    <h5>Connection Settings</h5>
                    <div class="code-block">
                        <strong>qBittorrent Host:</strong> IP address or hostname<br>
                        <strong>Port:</strong> Web UI port (default: 8080)<br>
                        <strong>Username/Password:</strong> Web UI credentials
                    </div>

                    <h5 class="mt-3">Management Parameters</h5>
                    <ul>
                        <li><strong>Seed Limit:</strong> Minimum number of other seeders required before auto-stop</li>
                        <li><strong>Minimum Ratio:</strong> Required ratio threshold for auto-stop eligibility</li>
                        <li><strong>Category:</strong> Target category for management (e.g., "SEED")</li>
                        <li><strong>Check Interval:</strong> Frequency of automatic checks (seconds)</li>
                    </ul>

                    <div class="warning-box">
                        <h6><i class="bi bi-shield-check"></i> Security Note</h6>
                        <p class="mb-0">Ensure your qBittorrent Web UI is properly secured, especially if accessible over a network. Use strong credentials and consider IP restrictions.</p>
                    </div>
                </section>

                <!-- Troubleshooting Section -->
                <section id="troubleshooting" class="doc-section">
                    <h2 class="doc-header"><i class="bi bi-tools"></i> Troubleshooting</h2>

                    <h5>Common Issues</h5>

                    <div class="feature-card">
                        <h6><i class="bi bi-x-circle text-danger"></i> Connection Failed</h6>
                        <ul class="mb-0">
                            <li>Verify qBittorrent Web UI is enabled and accessible</li>
                            <li>Check host, port, username, and password settings</li>
                            <li>Ensure no firewall blocking the connection</li>
                            <li>Test connection using the "Test Connection" button</li>
                        </ul>
                    </div>

                    <div class="feature-card">
                        <h6><i class="bi bi-exclamation-circle text-warning"></i> Seeds Showing as 0</h6>
                        <ul class="mb-0">
                            <li><strong>Automatic Fix:</strong> The app now temporarily starts completed/stopped/paused torrents to get accurate seed counts</li>
                            <li>During checks, you may see torrents briefly start then pause/stop again</li>
                            <li>This includes torrents in stoppedUP, pausedUP, stalledUP, and queuedUP states</li>
                            <li>This is normal behavior to ensure accurate seed count detection</li>
                            <li>Private trackers may still show 0 if tracker doesn't report peer counts</li>
                            <li>Force-started torrents are never auto-stopped regardless of seed count</li>
                        </ul>
                    </div>

                    <div class="feature-card">
                        <h6><i class="bi bi-arrow-clockwise text-info"></i> Torrents Not Auto-Pausing</h6>
                        <ul class="mb-0">
                            <li>Verify both seed limit and ratio conditions are met</li>
                            <li>Check if torrents are force-started (lightning icon)</li>
                            <li>Ensure torrents are in the correct category</li>
                            <li>Review management activity log for details</li>
                        </ul>
                    </div>
                </section>

                <!-- API Reference Section -->
                <section id="api" class="doc-section">
                    <h2 class="doc-header"><i class="bi bi-code-slash"></i> API Reference</h2>

                    <h5>Available Endpoints</h5>
                    <div class="code-block">
                        GET  /api/status      - Application status and statistics<br>
                        GET  /api/torrents    - List all torrents in category<br>
                        GET  /api/activity    - Recent management activity<br>
                        POST /api/connect     - Test qBittorrent connection<br>
                        POST /api/check       - Trigger manual torrent check<br>
                        POST /api/torrent/{hash}/resume      - Resume torrent<br>
                        POST /api/torrent/{hash}/pause       - Pause torrent<br>
                        POST /api/torrent/{hash}/force-check - Force check torrent<br>
                        POST /api/torrent/{hash}/force-start - Force start torrent
                    </div>

                    <h5 class="mt-3">Response Format</h5>
                    <p>All API responses follow a consistent JSON format:</p>
                    <div class="code-block">
                        {<br>
                        &nbsp;&nbsp;"success": true|false,<br>
                        &nbsp;&nbsp;"message": "Status message",<br>
                        &nbsp;&nbsp;"data": { ... }<br>
                        }
                    </div>
                </section>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Theme Toggle Script -->
    <script>
        // Simple theme toggle for info page
        const darkMode = localStorage.getItem('darkMode') === 'true';
        if (darkMode) {
            document.documentElement.setAttribute('data-theme', 'dark');
            document.getElementById('darkModeToggle').innerHTML = '<i class="bi bi-sun"></i>';
        }
        
        document.getElementById('darkModeToggle').addEventListener('click', () => {
            const isDark = document.documentElement.getAttribute('data-theme') === 'dark';
            document.documentElement.setAttribute('data-theme', isDark ? 'light' : 'dark');
            localStorage.setItem('darkMode', !isDark);
            document.getElementById('darkModeToggle').innerHTML = isDark ? '<i class="bi bi-moon"></i>' : '<i class="bi bi-sun"></i>';
        });
    </script>
</body>
</html>
