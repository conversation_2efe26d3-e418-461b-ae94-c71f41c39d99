<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - qBittorrent Seed Manager</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">

    <style>
        .metric-card {
            background: linear-gradient(135deg, var(--color-blue) 0%, var(--color-indigo) 100%);
            color: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            border: none;
            box-shadow: 0 4px 12px rgba(0, 122, 255, 0.2);
        }

        .metric-value {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .metric-label {
            font-size: 0.9rem;
            opacity: 0.9;
            font-weight: 500;
        }

        .activity-item {
            border-left: 3px solid var(--color-blue);
            padding-left: 15px;
            margin-bottom: 15px;
            transition: all 0.2s ease;
        }

        .activity-item:hover {
            background-color: var(--color-tertiary-system-fill);
            border-radius: 8px;
            padding: 10px 15px;
            margin-left: -10px;
        }

        .activity-time {
            font-size: 0.8rem;
            color: var(--color-tertiary-label);
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin-bottom: 20px;
        }

        .top-torrent {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            border-bottom: 1px solid var(--color-separator);
            transition: background-color 0.2s ease;
        }

        .top-torrent:hover {
            background-color: var(--color-tertiary-system-fill);
        }

        .top-torrent:last-child {
            border-bottom: none;
        }

        .torrent-name {
            flex: 1;
            font-weight: 500;
            margin-right: 10px;
            color: var(--color-label);
        }

        .torrent-metric {
            font-weight: 600;
            color: var(--color-green);
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-cloud-download"></i>
                qBittorrent Seed Manager
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link me-3" href="/">
                    <i class="bi bi-list-ul"></i> Torrents
                </a>
                <a class="nav-link me-3" href="/info">
                    <i class="bi bi-info-circle"></i> Info
                </a>
                <button class="btn btn-outline-light btn-sm me-2" id="darkModeToggle">
                    <i class="bi bi-moon"></i>
                </button>
                <span class="navbar-text">
                    <span class="status-indicator" id="connectionStatus"></span>
                    <span id="connectionText">Disconnected</span>
                </span>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Key Metrics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="metric-card text-center">
                    <div class="metric-value" id="totalTorrentsMetric">0</div>
                    <div class="metric-label">Total Torrents</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card text-center">
                    <div class="metric-value" id="activeSeedersMetric">0</div>
                    <div class="metric-label">Active Seeders</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card text-center">
                    <div class="metric-value" id="totalPeersMetric">0</div>
                    <div class="metric-label">Connected Peers</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card text-center">
                    <div class="metric-value" id="avgRatioMetric">0.00</div>
                    <div class="metric-label">Average Ratio</div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-pie-chart"></i> Torrent States
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="stateChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-bar-chart"></i> Upload Activity (Last 24h)
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="uploadChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Torrents and Recent Activity -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-trophy"></i> Top Torrents by Ratio
                        </h5>
                    </div>
                    <div class="card-body" id="topRatioTorrents">
                        <div class="text-center text-muted">Loading...</div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-activity"></i> Recent Management Activity
                        </h5>
                    </div>
                    <div class="card-body" id="recentActivity">
                        <div class="text-center text-muted">Loading...</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Most Active Torrents -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-upload"></i> Most Active Uploads
                        </h5>
                    </div>
                    <div class="card-body" id="topUploadTorrents">
                        <div class="text-center text-muted">Loading...</div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-people"></i> Most Popular Torrents
                        </h5>
                    </div>
                    <div class="card-body" id="topPeerTorrents">
                        <div class="text-center text-muted">Loading...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Dashboard JavaScript -->
    <script src="{{ url_for('static', filename='js/dashboard.js') }}"></script>
</body>
</html>
