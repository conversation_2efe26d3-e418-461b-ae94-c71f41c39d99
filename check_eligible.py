import requests
import json

# Get torrents
response = requests.get('http://localhost:5000/api/torrents')
data = response.json()

# Get config
config_response = requests.get('http://localhost:5000/api/config')
config = config_response.json()

print(f"Current config:")
print(f"  Action type: {config['action_type']}")
print(f"  Seed limit: {config['seed_limit']}")
print(f"  Min ratio: {config['min_ratio']}")

# Find eligible torrents
eligible = []
for t in data:
    if (t['num_seeds'] >= config['seed_limit'] and 
        t['ratio'] >= config['min_ratio'] and 
        t['state'] in ['uploading', 'stalledUP', 'queuedUP']):
        eligible.append(t)

print(f"\nTorrents eligible for auto-management ({len(eligible)}):")
for t in eligible[:5]:
    print(f"  {t['name'][:50]}... - Seeds: {t['num_seeds']}, Ratio: {t['ratio']:.2f}, State: {t['state']}")

if eligible:
    print(f"\nFirst eligible torrent hash: {eligible[0]['hash']}")
