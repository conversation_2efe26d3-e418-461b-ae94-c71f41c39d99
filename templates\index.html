<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>qBittorrent Seed Manager</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">

    <style>
        /* Metric Cards with Apple-style gradients */
        .card-stat {
            background: linear-gradient(135deg, var(--color-blue) 0%, var(--color-indigo) 100%);
            color: white;
            border-radius: 12px;
            border: none;
            box-shadow: 0 4px 12px rgba(0, 122, 255, 0.2);
        }

        .refresh-animation {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bi bi-cloud-download"></i>
                qBittorrent Seed Manager
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link me-3" href="/dashboard">
                    <i class="bi bi-graph-up"></i> Dashboard
                </a>
                <a class="nav-link me-3" href="/info">
                    <i class="bi bi-info-circle"></i> Info
                </a>
                <button class="btn btn-outline-light btn-sm me-2" id="darkModeToggle">
                    <i class="bi bi-moon"></i>
                </button>
                <span class="navbar-text">
                    <span class="status-indicator" id="connectionStatus"></span>
                    <span id="connectionText">Disconnected</span>
                </span>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Stats Cards -->
        <div class="row mb-4">
            <div class="col-md-2">
                <div class="card card-stat text-center">
                    <div class="card-body">
                        <h5 class="card-title" id="totalTorrents">0</h5>
                        <p class="card-text">Total Torrents</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card card-stat text-center">
                    <div class="card-body">
                        <h5 class="card-title" id="pausedTorrents">0</h5>
                        <p class="card-text">Paused</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card card-stat text-center">
                    <div class="card-body">
                        <h5 class="card-title" id="seedingTorrents">0</h5>
                        <p class="card-text">Seeding</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card card-stat text-center">
                    <div class="card-body">
                        <h5 class="card-title" id="avgRatio">0.00</h5>
                        <p class="card-text">Avg Ratio</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card card-stat text-center">
                    <div class="card-body">
                        <h5 class="card-title" id="totalPeers">0</h5>
                        <p class="card-text">Total Peers</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card card-stat text-center">
                    <div class="card-body">
                        <h5 class="card-title" id="totalSeeders">0</h5>
                        <p class="card-text">Total Seeders</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Control Panel -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Control Panel</h5>
                    </div>
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <button class="btn btn-primary me-2" id="testConnectionBtn">
                                    <i class="bi bi-wifi"></i> Test Connection
                                </button>
                                <button class="btn btn-success me-2" id="forceCheckBtn">
                                    <i class="bi bi-arrow-clockwise"></i> Force Check All
                                </button>
                            </div>
                            <div class="col-md-6">
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <div class="input-group input-group-sm">
                                            <span class="input-group-text">Category:</span>
                                            <input type="text" class="form-control" id="quickCategoryInput" placeholder="movies">
                                            <button class="btn btn-outline-primary" type="button" id="quickCategoryBtn" title="Switch category">
                                                <i class="bi bi-check"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-6 text-end">
                                        <small class="text-muted">
                                            Last Check: <span id="lastCheck">Never</span>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Settings</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-outline-secondary btn-sm" data-bs-toggle="modal" data-bs-target="#settingsModal">
                            <i class="bi bi-gear"></i> Configure
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Torrents Table -->
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h5 class="mb-0">
                            Torrents
                            <span class="badge bg-primary ms-2" id="currentCategoryBadge">movies</span>
                            <span class="badge bg-secondary ms-1" id="filteredCount">0</span>
                        </h5>
                    </div>
                    <div class="col-md-6 text-end">
                        <button class="btn btn-sm btn-outline-secondary me-2" data-bs-toggle="collapse" data-bs-target="#filterPanel">
                            <i class="bi bi-funnel"></i> Filters
                        </button>
                        <button class="btn btn-sm btn-outline-primary" id="refreshTorrentsBtn">
                            <i class="bi bi-arrow-clockwise" id="refreshIcon"></i>
                        </button>
                    </div>
                </div>

                <!-- Filter Panel -->
                <div class="collapse mt-3" id="filterPanel">
                    <div class="row g-2">
                        <div class="col-md-3">
                            <label class="form-label">Search Name</label>
                            <input type="text" class="form-control form-control-sm" id="filterName" placeholder="Filter by name...">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">State</label>
                            <select class="form-select form-select-sm" id="filterState">
                                <option value="">All States</option>
                                <option value="uploading">Seeding</option>
                                <option value="forcedUP">Force Seeding</option>
                                <option value="stalledUP">Stalled</option>
                                <option value="pausedUP">Paused</option>
                                <option value="stoppedUP">Stopped</option>
                                <option value="checkingUP">Checking</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Min Ratio</label>
                            <input type="number" class="form-control form-control-sm" id="filterMinRatio" placeholder="0.0" step="0.1">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Max Ratio</label>
                            <input type="number" class="form-control form-control-sm" id="filterMaxRatio" placeholder="100.0" step="0.1">
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">Has Peers</label>
                            <select class="form-select form-select-sm" id="filterHasPeers">
                                <option value="">All</option>
                                <option value="yes">Yes</option>
                                <option value="no">No</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">Actions</label>
                            <div>
                                <button class="btn btn-sm btn-outline-danger" id="clearFiltersBtn">
                                    <i class="bi bi-x"></i> Clear
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover table-sm" id="torrentsTable">
                        <thead>
                            <tr>
                                <th style="width: 35%; cursor: pointer;" data-sort="name">
                                    Name <i class="bi bi-arrow-down-up sort-icon"></i>
                                </th>
                                <th style="width: 8%; cursor: pointer;" data-sort="size">
                                    Size <i class="bi bi-arrow-down-up sort-icon"></i>
                                </th>
                                <th style="width: 8%; cursor: pointer;" data-sort="progress">
                                    Progress <i class="bi bi-arrow-down-up sort-icon"></i>
                                </th>
                                <th style="width: 10%; cursor: pointer;" data-sort="state">
                                    Status <i class="bi bi-arrow-down-up sort-icon"></i>
                                </th>
                                <th style="width: 8%; cursor: pointer;" data-sort="ratio">
                                    Ratio <i class="bi bi-arrow-down-up sort-icon"></i>
                                </th>
                                <th style="width: 10%; cursor: pointer;" data-sort="num_seeds">
                                    Seeds <i class="bi bi-arrow-down-up sort-icon"></i>
                                </th>
                                <th style="width: 10%; cursor: pointer;" data-sort="num_leechs">
                                    Peers <i class="bi bi-arrow-down-up sort-icon"></i>
                                </th>
                                <th style="width: 8%; cursor: pointer;" data-sort="dlspeed">
                                    DL Speed <i class="bi bi-arrow-down-up sort-icon"></i>
                                </th>
                                <th style="width: 8%; cursor: pointer;" data-sort="upspeed">
                                    UP Speed <i class="bi bi-arrow-down-up sort-icon"></i>
                                </th>
                                <th style="width: 15%;">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="torrentsTableBody">
                            <tr>
                                <td colspan="10" class="text-center">Loading torrents...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div class="modal fade" id="settingsModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Settings</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="settingsForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">qBittorrent Host</label>
                                    <input type="text" class="form-control" id="qbHost" name="qb_host">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Port</label>
                                    <input type="number" class="form-control" id="qbPort" name="qb_port">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Username</label>
                                    <input type="text" class="form-control" id="qbUsername" name="qb_username">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Password</label>
                                    <input type="password" class="form-control" id="qbPassword" name="qb_password">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Seed Limit</label>
                                    <input type="number" class="form-control" id="seedLimit" name="seed_limit" min="1" max="100">
                                    <div class="form-text">Minimum number of seeders before pausing</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Min Ratio</label>
                                    <input type="number" step="0.1" class="form-control" id="minRatio" name="min_ratio" min="0.1" max="10">
                                    <div class="form-text">Minimum ratio before pausing</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Category to Monitor</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="category" name="category" placeholder="e.g., movies, tv, music">
                                        <button class="btn btn-outline-secondary" type="button" id="loadCategoriesBtn" title="Load available categories">
                                            <i class="bi bi-arrow-clockwise"></i>
                                        </button>
                                    </div>
                                    <div class="form-text">Category name from qBittorrent (case-sensitive)</div>
                                    <div id="availableCategories" class="mt-2" style="display: none;">
                                        <small class="text-muted">Available categories:</small>
                                        <div id="categoriesList" class="mt-1"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Check Interval (seconds)</label>
                            <input type="number" class="form-control" id="checkInterval" name="check_interval">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveSettingsBtn">Save Settings</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
