#!/usr/bin/env python3
"""
qBittorrent Seed Manager - Run Script
Simple script to start the application with proper error handling
"""

import sys
import os
import logging
from app import app, seed_manager

def main():
    """Main entry point for the application"""
    print("🌱 qBittorrent Seed Manager")
    print("=" * 50)
    
    # Check if config exists
    if not os.path.exists('config.json'):
        print("⚠️  Warning: config.json not found. Using default/environment configuration.")
    
    # Test qBittorrent connection on startup
    print("🔌 Testing qBittorrent connection...")
    if seed_manager.connect_qbittorrent():
        print("✅ Connected to qBittorrent successfully!")
        
        # Get some basic info
        try:
            torrents = seed_manager.get_torrents_by_category()
            print(f"📊 Found {len(torrents)} torrents in category '{seed_manager.config['category']}'")
        except Exception as e:
            print(f"⚠️  Could not fetch torrents: {e}")
    else:
        print("❌ Failed to connect to qBittorrent. Please check your configuration.")
        print("   The application will still start, but functionality will be limited.")
    
    print("\n🚀 Starting web server...")
    print(f"📱 Web interface will be available at: http://localhost:5000")
    print("🔄 Automatic checks will run every {} seconds".format(seed_manager.config['check_interval']))
    print("\nPress Ctrl+C to stop the application")
    print("=" * 50)
    
    try:
        # Start the Flask application
        app.run(
            debug=False,
            host='0.0.0.0',
            port=5000,
            use_reloader=False  # Disable reloader to prevent double startup
        )
    except KeyboardInterrupt:
        print("\n\n🛑 Application stopped by user")
    except Exception as e:
        print(f"\n❌ Application error: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
