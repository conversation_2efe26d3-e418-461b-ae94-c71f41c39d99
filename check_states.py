import requests
import json

# Get torrents
response = requests.get('http://localhost:5000/api/torrents')
data = response.json()

# Count states
states = {}
for torrent in data:
    state = torrent['state']
    states[state] = states.get(state, 0) + 1

print("Torrent states:")
for state, count in sorted(states.items()):
    print(f"  {state}: {count}")

# Check for paused torrents
paused = [t for t in data if t['state'] == 'pausedUP']
print(f"\nPaused torrents ({len(paused)}):")
for t in paused[:5]:
    print(f"  {t['name'][:50]}...")

# Check for stopped torrents
stopped = [t for t in data if t['state'] == 'stoppedUP']
print(f"\nStopped torrents ({len(stopped)}):")
for t in stopped[:5]:
    print(f"  {t['name'][:50]}...")

# Check Chicago Fire specifically
chicago = [t for t in data if 'chicago fire' in t['name'].lower() and 's04' in t['name'].lower()]
if chicago:
    print(f"\nChicago Fire S04 state: {chicago[0]['state']}")
    print(f"Chicago Fire S04 ratio: {chicago[0]['ratio']:.2f}")
    print(f"Chicago Fire S04 seeds: {chicago[0]['num_seeds']}")
