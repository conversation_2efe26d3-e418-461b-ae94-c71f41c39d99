# qBittorrent Seed Manager

A complete Python web application for managing seeding behavior in qBittorrent using the Web API. This application provides both a backend service and a modern, responsive frontend UI for automated torrent management.

## Features

### Backend Features
- **qBittorrent Web API Integration**: Uses `qbittorrent-api` for seamless communication
- **Automated Seeding Management**: Pause/resume torrents based on configurable criteria
- **RESTful API**: Complete API for torrent management and configuration
- **Flexible Configuration**: Support for both JSON config files and environment variables
- **Scheduled Checks**: Automatic periodic torrent evaluation
- **Comprehensive Logging**: Detailed logging of all actions and errors

### Frontend Features
- **Modern Responsive UI**: Built with Bootstrap 5 for mobile and desktop
- **Real-time Dashboard**: Live torrent status and statistics
- **Interactive Controls**: Manual torrent management with one-click actions
- **Dark Mode Support**: Toggle between light and dark themes
- **Settings Panel**: Easy configuration management through the UI
- **Auto-refresh**: Automatic updates every 30 seconds

### Seeding Logic
- **Smart Pause/Resume**: Temporarily resume paused torrents to refresh peer data
- **Configurable Criteria**: Set custom seed limits and minimum ratios
- **Category-based**: Manage torrents by specific categories
- **Ratio Protection**: Ensure minimum seeding ratios before pausing

## Installation

### Prerequisites
- Python 3.8 or higher
- qBittorrent with Web UI enabled
- Modern web browser

### Quick Start

1. **Clone or download the application**
   ```bash
   git clone <repository-url>
   cd seedManager
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure qBittorrent settings**
   
   Edit `config.json` or create a `.env` file:
   ```json
   {
     "qb_host": "your-qbittorrent-host",
     "qb_port": 8080,
     "qb_username": "admin",
     "qb_password": "your-password",
     "seed_limit": 5,
     "min_ratio": 2.0,
     "category": "movies",
     "check_interval": 300
   }
   ```

4. **Run the application**
   ```bash
   python app.py
   ```

5. **Access the web interface**
   
   Open your browser and navigate to: `http://localhost:5000`

## Configuration

### Configuration Options

| Setting | Description | Default |
|---------|-------------|---------|
| `qb_host` | qBittorrent host address | localhost |
| `qb_port` | qBittorrent Web UI port | 8080 |
| `qb_username` | qBittorrent username | admin |
| `qb_password` | qBittorrent password | adminpass |
| `seed_limit` | Minimum seeders before pausing | 5 |
| `min_ratio` | Minimum ratio before pausing | 2.0 |
| `category` | Torrent category to manage | movies |
| `check_interval` | Check interval in seconds | 300 |

### Environment Variables

You can also use environment variables (they override config.json):

```bash
export QB_HOST=qbt.wikizell.com
export QB_PORT=443
export QB_USERNAME=admin
export QB_PASSWORD=your-password
export SEED_LIMIT=5
export MIN_RATIO=2.0
export CATEGORY=movies
export CHECK_INTERVAL=300
```

## API Endpoints

### Status and Information
- `GET /api/status` - Get application status and statistics
- `GET /api/torrents` - Get all torrents in configured category

### Connection Management
- `POST /api/connect` - Test qBittorrent connection

### Torrent Management
- `POST /api/check` - Manually trigger torrent check
- `POST /api/torrent/<hash>/resume` - Resume specific torrent
- `POST /api/torrent/<hash>/pause` - Pause specific torrent
- `POST /api/torrent/<hash>/force-check` - Force check specific torrent

### Configuration
- `GET /api/config` - Get current configuration
- `POST /api/config` - Update configuration

## Usage

### Web Interface

1. **Dashboard**: View real-time statistics and torrent status
2. **Test Connection**: Verify qBittorrent connectivity
3. **Force Check**: Manually trigger torrent evaluation
4. **Settings**: Configure all parameters through the UI
5. **Individual Controls**: Pause, resume, or force-check specific torrents

### Automated Operation

The application automatically:
1. Connects to qBittorrent on startup
2. Runs scheduled checks based on `check_interval`
3. Evaluates torrents in the specified category
4. Pauses torrents meeting both criteria (seeds ≥ limit AND ratio ≥ minimum)
5. Temporarily resumes paused torrents to refresh peer data

## Docker Support

### Build and Run with Docker

```bash
# Build the image
docker build -t qbt-seed-manager .

# Run the container
docker run -d \
  --name seed-manager \
  -p 5000:5000 \
  -v $(pwd)/config.json:/app/config.json \
  -v $(pwd)/seed_manager.log:/app/seed_manager.log \
  qbt-seed-manager
```

### Docker Compose

```yaml
version: '3.8'
services:
  seed-manager:
    build: .
    ports:
      - "5000:5000"
    volumes:
      - ./config.json:/app/config.json
      - ./seed_manager.log:/app/seed_manager.log
    environment:
      - QB_HOST=your-qbittorrent-host
      - QB_PORT=8080
      - QB_USERNAME=admin
      - QB_PASSWORD=your-password
```

## Troubleshooting

### Common Issues

1. **Connection Failed**
   - Verify qBittorrent Web UI is enabled
   - Check host, port, username, and password
   - Ensure firewall allows connections

2. **No Torrents Found**
   - Verify the category name matches exactly
   - Check that torrents exist in the specified category

3. **Torrents Not Being Managed**
   - Check the seed limit and ratio settings
   - Verify torrents meet both criteria for pausing
   - Review logs for detailed information

### Logs

Check `seed_manager.log` for detailed operation logs:
```bash
tail -f seed_manager.log
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review the logs for error details
3. Open an issue on the repository
