#!/usr/bin/env python3
"""
Test suite for qBittorrent Seed Manager
"""

import unittest
import json
import tempfile
import os
from unittest.mock import Mock, patch, MagicMock
from app import SeedManager, app

class TestSeedManager(unittest.TestCase):
    def setUp(self):
        """Set up test fixtures"""
        self.seed_manager = SeedManager()
        self.app = app.test_client()
        self.app.testing = True
        
        # Mock config
        self.test_config = {
            'qb_host': 'localhost',
            'qb_port': 8080,
            'qb_username': 'admin',
            'qb_password': 'password',
            'seed_limit': 5,
            'min_ratio': 2.0,
            'category': 'test',
            'check_interval': 300
        }
    
    def test_load_config_from_json(self):
        """Test loading configuration from JSON file"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(self.test_config, f)
            config_file = f.name
        
        # Temporarily replace config.json
        original_exists = os.path.exists
        with patch('os.path.exists') as mock_exists:
            mock_exists.side_effect = lambda path: path == 'config.json'
            with patch('builtins.open', unittest.mock.mock_open(read_data=json.dumps(self.test_config))):
                config = self.seed_manager.load_config()
                
        self.assertEqual(config['qb_host'], 'localhost')
        self.assertEqual(config['seed_limit'], 5)
        
        # Clean up
        os.unlink(config_file)
    
    def test_load_config_from_env(self):
        """Test loading configuration from environment variables"""
        with patch.dict(os.environ, {
            'QB_HOST': 'test-host',
            'QB_PORT': '9090',
            'SEED_LIMIT': '10'
        }):
            config = self.seed_manager.load_config()
            self.assertEqual(config['qb_host'], 'test-host')
            self.assertEqual(config['qb_port'], 9090)
            self.assertEqual(config['seed_limit'], 10)
    
    @patch('qbittorrentapi.Client')
    def test_connect_qbittorrent_success(self, mock_client):
        """Test successful qBittorrent connection"""
        mock_instance = Mock()
        mock_client.return_value = mock_instance
        
        result = self.seed_manager.connect_qbittorrent()
        
        self.assertTrue(result)
        mock_instance.auth_log_in.assert_called_once()
    
    @patch('qbittorrentapi.Client')
    def test_connect_qbittorrent_failure(self, mock_client):
        """Test failed qBittorrent connection"""
        mock_instance = Mock()
        mock_instance.auth_log_in.side_effect = Exception("Connection failed")
        mock_client.return_value = mock_instance
        
        result = self.seed_manager.connect_qbittorrent()
        
        self.assertFalse(result)
        self.assertIsNone(self.seed_manager.qb_client)
    
    def test_torrent_to_dict(self):
        """Test torrent object to dictionary conversion"""
        mock_torrent = Mock()
        mock_torrent.hash = 'test_hash'
        mock_torrent.name = 'Test Torrent'
        mock_torrent.size = 1000000
        mock_torrent.progress = 1.0
        mock_torrent.ratio = 2.5
        mock_torrent.num_seeds = 10
        mock_torrent.num_leechs = 2
        mock_torrent.state = 'uploading'
        mock_torrent.category = 'test'
        mock_torrent.priority = 1
        mock_torrent.downloaded = 1000000
        mock_torrent.uploaded = 2500000
        mock_torrent.dlspeed = 0
        mock_torrent.upspeed = 1000
        mock_torrent.eta = 0
        mock_torrent.added_on = 1234567890
        mock_torrent.completion_on = 1234567900
        
        result = self.seed_manager._torrent_to_dict(mock_torrent)
        
        self.assertEqual(result['hash'], 'test_hash')
        self.assertEqual(result['name'], 'Test Torrent')
        self.assertEqual(result['ratio'], 2.5)
        self.assertEqual(result['num_seeds'], 10)
    
    def test_get_torrents_by_category_no_client(self):
        """Test getting torrents when no client is connected"""
        self.seed_manager.qb_client = None
        result = self.seed_manager.get_torrents_by_category()
        self.assertEqual(result, [])
    
    @patch('app.seed_manager')
    def test_api_status_endpoint(self, mock_seed_manager):
        """Test the /api/status endpoint"""
        mock_seed_manager.qb_client = Mock()
        mock_seed_manager.last_check = None
        mock_seed_manager.config = self.test_config
        mock_seed_manager.stats = {'total_torrents': 5}
        
        response = self.app.get('/api/status')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertTrue(data['connected'])
        self.assertEqual(data['config']['qb_host'], 'localhost')
    
    @patch('app.seed_manager')
    def test_api_torrents_endpoint(self, mock_seed_manager):
        """Test the /api/torrents endpoint"""
        mock_torrents = [
            {'hash': 'hash1', 'name': 'Torrent 1'},
            {'hash': 'hash2', 'name': 'Torrent 2'}
        ]
        mock_seed_manager.get_torrents_by_category.return_value = mock_torrents
        
        response = self.app.get('/api/torrents')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertEqual(len(data), 2)
        self.assertEqual(data[0]['name'], 'Torrent 1')
    
    @patch('app.seed_manager')
    def test_api_connect_endpoint(self, mock_seed_manager):
        """Test the /api/connect endpoint"""
        mock_seed_manager.connect_qbittorrent.return_value = True
        
        response = self.app.post('/api/connect')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertTrue(data['success'])
    
    @patch('app.seed_manager')
    def test_api_check_endpoint(self, mock_seed_manager):
        """Test the /api/check endpoint"""
        response = self.app.post('/api/check')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        mock_seed_manager.check_and_manage_torrents.assert_called_once()
    
    @patch('app.seed_manager')
    def test_api_resume_torrent_endpoint(self, mock_seed_manager):
        """Test the /api/torrent/<hash>/resume endpoint"""
        response = self.app.post('/api/torrent/test_hash/resume')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        mock_seed_manager._resume_torrent.assert_called_once_with('test_hash')
    
    @patch('app.seed_manager')
    def test_api_config_get_endpoint(self, mock_seed_manager):
        """Test the GET /api/config endpoint"""
        mock_seed_manager.config = self.test_config
        
        response = self.app.get('/api/config')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertEqual(data['qb_host'], 'localhost')
    
    @patch('app.seed_manager')
    def test_api_config_post_endpoint(self, mock_seed_manager):
        """Test the POST /api/config endpoint"""
        new_config = {'seed_limit': 10, 'min_ratio': 3.0}
        
        response = self.app.post('/api/config', 
                               data=json.dumps(new_config),
                               content_type='application/json')
        self.assertEqual(response.status_code, 200)
        
        data = json.loads(response.data)
        self.assertTrue(data['success'])
        mock_seed_manager.update_config.assert_called_once_with(new_config)

class TestSeedingLogic(unittest.TestCase):
    def setUp(self):
        """Set up test fixtures for seeding logic tests"""
        self.seed_manager = SeedManager()
        self.seed_manager.config = {
            'seed_limit': 5,
            'min_ratio': 2.0,
            'category': 'test'
        }
    
    @patch('time.sleep')
    def test_check_and_manage_torrents_pause_logic(self, mock_sleep):
        """Test the logic for pausing torrents"""
        # Mock qBittorrent client
        mock_client = Mock()
        self.seed_manager.qb_client = mock_client
        
        # Mock torrent that should be paused (high seeds and ratio)
        mock_torrent = Mock()
        mock_torrent.hash = 'test_hash'
        mock_torrent.name = 'Test Torrent'
        mock_torrent.ratio = 3.0
        mock_torrent.num_seeds = 10
        mock_torrent.num_leechs = 1
        mock_torrent.state = 'uploading'
        
        mock_client.torrents_info.return_value = [mock_torrent]
        
        with patch.object(self.seed_manager, '_torrent_to_dict') as mock_to_dict:
            mock_to_dict.return_value = {
                'hash': 'test_hash',
                'name': 'Test Torrent',
                'ratio': 3.0,
                'num_seeds': 10,
                'num_leechs': 1,
                'state': 'uploading'
            }
            
            with patch.object(self.seed_manager, '_pause_torrent') as mock_pause:
                self.seed_manager.check_and_manage_torrents()
                mock_pause.assert_called_once_with('test_hash')

if __name__ == '__main__':
    unittest.main()
