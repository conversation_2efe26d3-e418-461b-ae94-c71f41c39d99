// qBittorrent Seed Manager Frontend JavaScript

class SeedManager {
    constructor() {
        this.apiBase = '/api';
        this.refreshInterval = null;
        this.darkMode = localStorage.getItem('darkMode') === 'true';
        this.allTorrents = []; // Store all torrents for filtering
        this.filteredTorrents = []; // Store filtered torrents
        this.sortField = 'name'; // Current sort field
        this.sortDirection = 'asc'; // Current sort direction

        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.applyDarkMode();
        this.loadStatus();
        this.loadTorrents();
        this.startAutoRefresh();
    }
    
    setupEventListeners() {
        // Dark mode toggle
        document.getElementById('darkModeToggle').addEventListener('click', () => {
            this.toggleDarkMode();
        });
        
        // Test connection button
        document.getElementById('testConnectionBtn').addEventListener('click', () => {
            this.testConnection();
        });
        
        // Force check button
        document.getElementById('forceCheckBtn').addEventListener('click', () => {
            this.forceCheck();
        });
        
        // Refresh torrents button
        document.getElementById('refreshTorrentsBtn').addEventListener('click', () => {
            this.refreshTorrents();
        });
        
        // Save settings button
        document.getElementById('saveSettingsBtn').addEventListener('click', () => {
            this.saveSettings();
        });

        // Load settings when modal opens
        document.getElementById('settingsModal').addEventListener('show.bs.modal', () => {
            this.loadSettings();
        });

        // Load categories button
        document.getElementById('loadCategoriesBtn').addEventListener('click', () => {
            this.loadCategories();
        });

        // Quick category change
        document.getElementById('quickCategoryBtn').addEventListener('click', () => {
            this.quickChangeCategory();
        });

        // Enter key on quick category input
        document.getElementById('quickCategoryInput').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.quickChangeCategory();
            }
        });

        // Filter event listeners
        document.getElementById('filterName').addEventListener('input', () => {
            this.applyFilters();
        });

        document.getElementById('filterState').addEventListener('change', () => {
            this.applyFilters();
        });

        document.getElementById('filterMinRatio').addEventListener('input', () => {
            this.applyFilters();
        });

        document.getElementById('filterMaxRatio').addEventListener('input', () => {
            this.applyFilters();
        });

        document.getElementById('filterHasPeers').addEventListener('change', () => {
            this.applyFilters();
        });

        document.getElementById('clearFiltersBtn').addEventListener('click', () => {
            this.clearFilters();
        });

        // Add sorting event listeners to table headers
        document.querySelectorAll('th[data-sort]').forEach(header => {
            header.addEventListener('click', () => {
                this.sortTable(header.dataset.sort);
            });
        });

        // Queue settings event listeners
        document.getElementById('queueSettingsModal').addEventListener('show.bs.modal', () => {
            this.loadQueueSettings();
        });

        document.getElementById('saveQueueSettingsBtn').addEventListener('click', () => {
            this.saveQueueSettings();
        });

        document.getElementById('applySuggestedBtn').addEventListener('click', () => {
            this.applySuggestedValues();
        });

        document.getElementById('disableQueueBtn').addEventListener('click', () => {
            this.disableQueue();
        });

        document.getElementById('queueingEnabled').addEventListener('change', (e) => {
            this.toggleQueueLimitsSection(e.target.checked);
        });
    }
    
    toggleDarkMode() {
        this.darkMode = !this.darkMode;
        localStorage.setItem('darkMode', this.darkMode);
        this.applyDarkMode();
    }
    
    applyDarkMode() {
        const html = document.documentElement;
        const icon = document.querySelector('#darkModeToggle i');

        if (this.darkMode) {
            html.setAttribute('data-theme', 'dark');
            icon.className = 'bi bi-sun';
        } else {
            html.setAttribute('data-theme', 'light');
            icon.className = 'bi bi-moon';
        }
    }
    
    async apiCall(endpoint, method = 'GET', data = null) {
        try {
            const options = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                }
            };
            
            if (data) {
                options.body = JSON.stringify(data);
            }
            
            const response = await fetch(`${this.apiBase}${endpoint}`, options);
            return await response.json();
        } catch (error) {
            console.error('API call failed:', error);
            this.showAlert('API call failed: ' + error.message, 'danger');
            return null;
        }
    }
    
    async loadStatus() {
        const status = await this.apiCall('/status');
        if (status) {
            this.updateConnectionStatus(status.connected);
            this.updateStats(status.stats);
            this.updateLastCheck(status.last_check);
            // Update quick category input and badge with current category
            if (status.config && status.config.category) {
                document.getElementById('quickCategoryInput').value = status.config.category;
                document.getElementById('currentCategoryBadge').textContent = status.config.category;
            }
        }
    }
    
    async loadTorrents() {
        const torrents = await this.apiCall('/torrents');
        if (torrents) {
            this.allTorrents = torrents;
            this.applyFilters();
        }
    }
    
    async testConnection() {
        const btn = document.getElementById('testConnectionBtn');
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Testing...';
        btn.disabled = true;
        
        const result = await this.apiCall('/connect', 'POST');
        
        if (result && result.success) {
            this.showAlert('Connection successful!', 'success');
            this.loadStatus();
        } else {
            this.showAlert('Connection failed!', 'danger');
        }
        
        btn.innerHTML = originalText;
        btn.disabled = false;
    }
    
    async forceCheck() {
        const btn = document.getElementById('forceCheckBtn');
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Checking...';
        btn.disabled = true;
        
        const result = await this.apiCall('/check', 'POST');
        
        if (result && result.success) {
            this.showAlert('Force check completed!', 'success');
            this.loadStatus();
            this.loadTorrents();
        } else {
            this.showAlert('Force check failed!', 'danger');
        }
        
        btn.innerHTML = originalText;
        btn.disabled = false;
    }
    
    async refreshTorrents() {
        const icon = document.getElementById('refreshIcon');
        icon.classList.add('refresh-animation');
        
        await this.loadTorrents();
        await this.loadStatus();
        
        setTimeout(() => {
            icon.classList.remove('refresh-animation');
        }, 1000);
    }
    
    async loadSettings() {
        const config = await this.apiCall('/config');
        if (config) {
            document.getElementById('qbHost').value = config.qb_host || '';
            document.getElementById('qbPort').value = config.qb_port || '';
            document.getElementById('qbUsername').value = config.qb_username || '';
            document.getElementById('qbPassword').value = config.qb_password || '';
            document.getElementById('seedLimit').value = config.seed_limit || '';
            document.getElementById('minRatio').value = config.min_ratio || '';
            document.getElementById('category').value = config.category || '';
            document.getElementById('checkInterval').value = config.check_interval || '';
            document.getElementById('actionType').value = config.action_type || 'stop';
        }
    }
    
    async saveSettings() {
        const formData = new FormData(document.getElementById('settingsForm'));
        const config = {};

        for (let [key, value] of formData.entries()) {
            if (key.includes('port') || key.includes('limit') || key.includes('interval')) {
                config[key] = parseInt(value);
            } else if (key.includes('ratio')) {
                config[key] = parseFloat(value);
            } else {
                config[key] = value;
            }
        }

        const result = await this.apiCall('/config', 'POST', config);

        if (result && result.success) {
            this.showAlert('Settings saved successfully!', 'success');
            bootstrap.Modal.getInstance(document.getElementById('settingsModal')).hide();
            this.loadStatus();
            this.loadTorrents();
            // Update quick category input
            document.getElementById('quickCategoryInput').value = config.category || '';
        } else {
            this.showAlert('Failed to save settings!', 'danger');
        }
    }

    async loadCategories() {
        const btn = document.getElementById('loadCategoriesBtn');
        const icon = btn.querySelector('i');
        icon.classList.add('refresh-animation');

        const result = await this.apiCall('/categories');

        if (result && result.success) {
            const categoriesDiv = document.getElementById('availableCategories');
            const categoriesList = document.getElementById('categoriesList');

            if (result.categories.length > 0) {
                categoriesList.innerHTML = result.categories.map(cat =>
                    `<span class="badge bg-secondary me-1 mb-1 category-badge" style="cursor: pointer;" onclick="seedManager.selectCategory('${cat}')">${cat}</span>`
                ).join('');
                categoriesDiv.style.display = 'block';
            } else {
                categoriesList.innerHTML = '<span class="text-muted">No categories found</span>';
                categoriesDiv.style.display = 'block';
            }
        } else {
            this.showAlert('Failed to load categories!', 'danger');
        }

        setTimeout(() => {
            icon.classList.remove('refresh-animation');
        }, 1000);
    }

    selectCategory(category) {
        document.getElementById('category').value = category;
        this.showAlert(`Selected category: ${category}`, 'info');
    }

    async quickChangeCategory() {
        const input = document.getElementById('quickCategoryInput');
        const category = input.value.trim();

        if (!category) {
            this.showAlert('Please enter a category name', 'warning');
            return;
        }

        const config = { category: category };
        const result = await this.apiCall('/config', 'POST', config);

        if (result && result.success) {
            this.showAlert(`Switched to category: ${category}`, 'success');
            document.getElementById('currentCategoryBadge').textContent = category;
            this.loadStatus();
            this.loadTorrents();
        } else {
            this.showAlert('Failed to change category!', 'danger');
        }
    }

    applyFilters() {
        if (!this.allTorrents || this.allTorrents.length === 0) {
            this.filteredTorrents = [];
            this.updateTorrentsTable([]);
            return;
        }

        const nameFilter = document.getElementById('filterName').value.toLowerCase();
        const stateFilter = document.getElementById('filterState').value;
        const minRatio = parseFloat(document.getElementById('filterMinRatio').value) || 0;
        const maxRatio = parseFloat(document.getElementById('filterMaxRatio').value) || Infinity;
        const hasPeersFilter = document.getElementById('filterHasPeers').value;

        this.filteredTorrents = this.allTorrents.filter(torrent => {
            // Name filter
            if (nameFilter && !torrent.name.toLowerCase().includes(nameFilter)) {
                return false;
            }

            // State filter
            if (stateFilter && torrent.state !== stateFilter) {
                return false;
            }

            // Ratio filters
            if (torrent.ratio < minRatio || torrent.ratio > maxRatio) {
                return false;
            }

            // Has peers filter
            if (hasPeersFilter === 'yes' && torrent.num_leechs === 0) {
                return false;
            }
            if (hasPeersFilter === 'no' && torrent.num_leechs > 0) {
                return false;
            }

            return true;
        });

        // Apply current sorting
        this.sortTable(this.sortField);
        this.updateFilteredCount();
    }

    clearFilters() {
        document.getElementById('filterName').value = '';
        document.getElementById('filterState').value = '';
        document.getElementById('filterMinRatio').value = '';
        document.getElementById('filterMaxRatio').value = '';
        document.getElementById('filterHasPeers').value = '';
        this.applyFilters();
    }

    updateFilteredCount() {
        const badge = document.getElementById('filteredCount');
        const total = this.allTorrents.length;
        const filtered = this.filteredTorrents.length;

        if (filtered === total) {
            badge.textContent = `${total}`;
            badge.className = 'badge bg-secondary ms-1';
        } else {
            badge.textContent = `${filtered}/${total}`;
            badge.className = 'badge bg-warning ms-1';
        }
    }

    sortTable(field) {
        // Toggle sort direction if clicking the same field
        if (this.sortField === field) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortField = field;
            this.sortDirection = 'asc';
        }

        // Update sort icons
        this.updateSortIcons();

        // Sort the filtered torrents
        this.filteredTorrents.sort((a, b) => {
            let aVal = a[field];
            let bVal = b[field];

            // Handle different data types
            if (field === 'name') {
                aVal = aVal.toLowerCase();
                bVal = bVal.toLowerCase();
            } else if (field === 'size' || field === 'dlspeed' || field === 'upspeed' ||
                      field === 'ratio' || field === 'progress' || field === 'num_seeds' || field === 'num_leechs') {
                aVal = parseFloat(aVal) || 0;
                bVal = parseFloat(bVal) || 0;
            }

            if (aVal < bVal) return this.sortDirection === 'asc' ? -1 : 1;
            if (aVal > bVal) return this.sortDirection === 'asc' ? 1 : -1;
            return 0;
        });

        // Update the table display
        this.updateTorrentsTable(this.filteredTorrents);
    }

    updateSortIcons() {
        // Reset all sort icons
        document.querySelectorAll('.sort-icon').forEach(icon => {
            icon.className = 'bi bi-arrow-down-up sort-icon';
        });

        // Set the active sort icon
        const activeHeader = document.querySelector(`th[data-sort="${this.sortField}"] .sort-icon`);
        if (activeHeader) {
            activeHeader.className = this.sortDirection === 'asc' ?
                'bi bi-sort-alpha-down sort-icon' : 'bi bi-sort-alpha-up sort-icon';
        }
    }
    
    updateConnectionStatus(connected) {
        const statusIndicator = document.getElementById('connectionStatus');
        const statusText = document.getElementById('connectionText');
        
        if (connected) {
            statusIndicator.className = 'status-indicator status-connected';
            statusText.textContent = 'Connected';
        } else {
            statusIndicator.className = 'status-indicator status-disconnected';
            statusText.textContent = 'Disconnected';
        }
    }
    
    updateStats(stats) {
        document.getElementById('totalTorrents').textContent = stats.total_torrents || 0;
        document.getElementById('pausedTorrents').textContent = stats.paused_torrents || 0;
        document.getElementById('seedingTorrents').textContent = stats.seeding_torrents || 0;
        document.getElementById('avgRatio').textContent = (stats.avg_ratio || 0).toFixed(2);
        document.getElementById('totalPeers').textContent = stats.total_peers || 0;
        document.getElementById('totalSeeders').textContent = stats.total_seeders || 0;
    }
    
    updateLastCheck(lastCheck) {
        const element = document.getElementById('lastCheck');
        if (lastCheck) {
            const date = new Date(lastCheck);
            element.textContent = date.toLocaleString();
        } else {
            element.textContent = 'Never';
        }
    }
    
    updateTorrentsTable(torrents) {
        const tbody = document.getElementById('torrentsTableBody');

        if (torrents.length === 0) {
            tbody.innerHTML = '<tr><td colspan="10" class="text-center">No torrents found</td></tr>';
            return;
        }

        tbody.innerHTML = torrents.map(torrent => `
            <tr>
                <td title="${torrent.name}" style="max-width: 300px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                    ${torrent.name}
                </td>
                <td>${this.formatBytes(torrent.size)}</td>
                <td>
                    <div class="progress" style="height: 16px;">
                        <div class="progress-bar" style="width: ${(torrent.progress * 100).toFixed(1)}%"></div>
                    </div>
                    <small class="text-muted">${(torrent.progress * 100).toFixed(1)}%</small>
                </td>
                <td>
                    <span class="torrent-state-${torrent.state}">
                        ${this.formatState(torrent.state)}
                    </span>
                    ${torrent.force_start ? '<i class="bi bi-lightning-fill text-warning ms-1" title="Force Started"></i>' : ''}
                </td>
                <td>
                    ${torrent.ratio >= 2.0 ?
                        `<span class="badge bg-success">${torrent.ratio.toFixed(2)}</span>` :
                        `<span class="text-muted">${torrent.ratio.toFixed(2)}</span>`
                    }
                </td>
                <td>
                    <span class="${torrent.num_seeds > 0 ? 'text-success fw-bold' : 'text-muted'}">${torrent.num_seeds}</span>
                    <span class="text-muted">(${torrent.num_seeds})</span>
                </td>
                <td>
                    <span class="${torrent.num_leechs > 0 ? 'text-info fw-bold' : 'text-muted'}">${torrent.num_leechs}</span>
                    <span class="text-muted">(${torrent.num_leechs})</span>
                </td>
                <td>
                    <span class="text-muted">${this.formatSpeed(torrent.dlspeed)}</span>
                </td>
                <td>
                    <span class="${torrent.upspeed > 0 ? 'text-success fw-bold' : 'text-muted'}">${this.formatSpeed(torrent.upspeed)}</span>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-success" onclick="seedManager.resumeTorrent('${torrent.hash}')" title="Resume">
                            <i class="bi bi-play"></i>
                        </button>
                        <button class="btn btn-outline-warning" onclick="seedManager.pauseTorrent('${torrent.hash}')" title="Pause">
                            <i class="bi bi-pause"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="seedManager.stopTorrent('${torrent.hash}')" title="Stop">
                            <i class="bi bi-stop"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="seedManager.forceCheckTorrent('${torrent.hash}')" title="Force Check">
                            <i class="bi bi-arrow-clockwise"></i>
                        </button>
                        <button class="btn btn-outline-primary" onclick="seedManager.forceStartTorrent('${torrent.hash}')" title="Force Start">
                            <i class="bi bi-lightning"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }
    
    async resumeTorrent(hash) {
        const result = await this.apiCall(`/torrent/${hash}/resume`, 'POST');
        if (result && result.success) {
            this.showAlert('Torrent resumed', 'success');
            this.refreshTorrents();
        }
    }
    
    async pauseTorrent(hash) {
        const result = await this.apiCall(`/torrent/${hash}/pause`, 'POST');
        if (result && result.success) {
            this.showAlert('Torrent paused', 'success');
            this.refreshTorrents();
        }
    }

    async stopTorrent(hash) {
        const result = await this.apiCall(`/torrent/${hash}/stop`, 'POST');
        if (result && result.success) {
            this.showAlert('Torrent stopped', 'success');
            this.refreshTorrents();
        }
    }
    
    async forceCheckTorrent(hash) {
        const result = await this.apiCall(`/torrent/${hash}/force-check`, 'POST');
        if (result && result.success) {
            this.showAlert('Force check initiated', 'success');
        }
    }

    async forceStartTorrent(hash) {
        const result = await this.apiCall(`/torrent/${hash}/force-start`, 'POST');
        if (result && result.success) {
            this.showAlert('Force start initiated', 'success');
            this.refreshTorrents();
        }
    }
    
    formatBytes(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    formatSpeed(bytesPerSecond) {
        if (bytesPerSecond === 0) return '0 B/s';
        const k = 1024;
        const sizes = ['B/s', 'KB/s', 'MB/s', 'GB/s'];
        const i = Math.floor(Math.log(bytesPerSecond) / Math.log(k));
        return parseFloat((bytesPerSecond / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }
    
    formatState(state) {
        const stateMap = {
            'uploading': 'Seeding',
            'forcedUP': 'Force Seeding',
            'pausedUP': 'Paused',
            'stalledUP': 'Stalled',
            'stoppedUP': 'Stopped',
            'checkingUP': 'Checking',
            'downloading': 'Downloading',
            'pausedDL': 'Paused',
            'error': 'Error',
            'queuedUP': 'Queued',
            'queuedDL': 'Queued'
        };
        return stateMap[state] || state;
    }
    
    showAlert(message, type = 'info') {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alertDiv);
        
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
    
    startAutoRefresh() {
        this.refreshInterval = setInterval(() => {
            this.loadStatus();
            this.loadTorrents();
        }, 30000); // Refresh every 30 seconds
    }
    
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
    }

    async loadQueueSettings() {
        const result = await this.apiCall('/queue-settings');
        if (result && result.current && result.suggestions) {
            const { current, suggestions } = result;

            // Update current values display
            document.getElementById('currentMaxUploads').textContent = current.max_active_uploads;
            document.getElementById('currentMaxTorrents').textContent = current.max_active_torrents;
            document.getElementById('currentMaxDownloads').textContent = current.max_active_downloads;

            // Update suggestions display
            document.getElementById('suggestedMaxUploads').textContent = suggestions.suggested_max_uploads;
            document.getElementById('suggestedMaxTorrents').textContent = suggestions.suggested_max_torrents;
            document.getElementById('suggestedMaxDownloads').textContent = suggestions.suggested_max_downloads;

            // Update statistics
            document.getElementById('totalTorrentsCount').textContent = suggestions.total_torrents;
            document.getElementById('completedTorrentsCount').textContent = suggestions.completed_torrents;
            document.getElementById('downloadingTorrentsCount').textContent = suggestions.downloading_torrents;

            // Set form values
            document.getElementById('queueingEnabled').checked = current.queueing_enabled;
            document.getElementById('maxActiveUploads').value = current.max_active_uploads;
            document.getElementById('maxActiveTorrents').value = current.max_active_torrents;
            document.getElementById('maxActiveDownloads').value = current.max_active_downloads;

            // Store suggestions for later use
            this.queueSuggestions = suggestions;

            // Toggle queue limits section based on queueing enabled
            this.toggleQueueLimitsSection(current.queueing_enabled);
        }
    }

    async saveQueueSettings() {
        const formData = new FormData(document.getElementById('queueSettingsForm'));
        const settings = {};

        for (let [key, value] of formData.entries()) {
            if (key === 'queueing_enabled') {
                settings[key] = document.getElementById('queueingEnabled').checked;
            } else {
                settings[key] = parseInt(value);
            }
        }

        const btn = document.getElementById('saveQueueSettingsBtn');
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Saving...';
        btn.disabled = true;

        const result = await this.apiCall('/queue-settings', 'POST', settings);

        if (result && result.success) {
            this.showAlert('Queue settings saved successfully!', 'success');
            bootstrap.Modal.getInstance(document.getElementById('queueSettingsModal')).hide();
            this.loadStatus();
            this.loadTorrents();
        } else {
            this.showAlert('Failed to save queue settings!', 'danger');
        }

        btn.innerHTML = originalText;
        btn.disabled = false;
    }

    applySuggestedValues() {
        if (this.queueSuggestions) {
            document.getElementById('maxActiveUploads').value = this.queueSuggestions.suggested_max_uploads;
            document.getElementById('maxActiveTorrents').value = this.queueSuggestions.suggested_max_torrents;
            document.getElementById('maxActiveDownloads').value = this.queueSuggestions.suggested_max_downloads;
            document.getElementById('queueingEnabled').checked = true;
            this.toggleQueueLimitsSection(true);
            this.showAlert('Applied suggested values!', 'info');
        }
    }

    disableQueue() {
        document.getElementById('queueingEnabled').checked = false;
        document.getElementById('maxActiveUploads').value = -1;
        document.getElementById('maxActiveTorrents').value = -1;
        document.getElementById('maxActiveDownloads').value = -1;
        this.toggleQueueLimitsSection(false);
        this.showAlert('Queue management disabled (unlimited torrents)!', 'warning');
    }

    toggleQueueLimitsSection(enabled) {
        const section = document.getElementById('queueLimitsSection');
        if (enabled) {
            section.style.display = 'block';
        } else {
            section.style.display = 'none';
        }
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.seedManager = new SeedManager();
});
