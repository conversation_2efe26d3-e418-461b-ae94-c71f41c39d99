import requests

# Get torrents
response = requests.get('http://localhost:5000/api/torrents')
data = response.json()

target_hash = '2f95b8b98d4c7e7f2407b58b41b4c9965fd5b609'
target = [t for t in data if t['hash'] == target_hash]

if target:
    t = target[0]
    print(f"Target torrent:")
    print(f"  Name: {t['name']}")
    print(f"  State: {t['state']}")
    print(f"  Hash: {t['hash']}")
else:
    print("Target torrent not found")
