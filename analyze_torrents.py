import requests
import json

# Get torrents and config
response = requests.get('http://localhost:5000/api/torrents')
data = response.json()

config_response = requests.get('http://localhost:5000/api/config')
config = config_response.json()

# Check the specific torrents mentioned
target1 = '1a3a4fa0f03b50cb37cb33ae4c0ce8e982214b0d'
target2 = '36b1e0ac648054d9200a58142b4ef4f994d326da'

print('=== CONFIGURATION ===')
print(f"Action type: {config['action_type']}")
print(f"Seed limit: {config['seed_limit']}")
print(f"Min ratio: {config['min_ratio']}")
print()

print('=== SPECIFIC TORRENTS ===')
for t in data:
    if t['hash'].startswith('1a3a4fa0') or t['hash'].startswith('36b1e0ac'):
        print(f"Hash: {t['hash']}")
        print(f"Name: {t['name'][:60]}...")
        print(f"State: {t['state']}")
        print(f"Seeds: {t['num_seeds']}")
        print(f"Ratio: {t['ratio']:.2f}")
        print(f"Progress: {t['progress']:.1%}")
        print(f"Should be managed: {t['num_seeds'] >= config['seed_limit'] and t['ratio'] >= config['min_ratio']}")
        print()

print('=== TORRENT STATE ANALYSIS ===')
states = {}
for t in data:
    state = t['state']
    states[state] = states.get(state, 0) + 1

for state, count in sorted(states.items()):
    print(f"{state}: {count}")

print()
print('=== TORRENTS WITH LOW SEEDS IN WRONG STATES ===')
low_seed_wrong_state = []
for t in data:
    if t['num_seeds'] < config['seed_limit'] and t['state'] in ['queuedUP', 'stoppedUP']:
        low_seed_wrong_state.append(t)
        
print(f"Found {len(low_seed_wrong_state)} torrents with <{config['seed_limit']} seeds in queuedUP/stoppedUP state:")
for t in low_seed_wrong_state[:10]:  # Show first 10
    print(f"  {t['name'][:50]}... - State: {t['state']}, Seeds: {t['num_seeds']}, Ratio: {t['ratio']:.2f}")

print()
print('=== TORRENTS THAT SHOULD BE SEEDING ===')
should_be_seeding = []
for t in data:
    if (t['progress'] >= 1.0 and  # Completed
        t['num_seeds'] < config['seed_limit'] and  # Low seeds
        t['state'] in ['queuedUP', 'stoppedUP']):  # Not seeding
        should_be_seeding.append(t)

print(f"Found {len(should_be_seeding)} completed torrents that should be seeding:")
for t in should_be_seeding[:10]:  # Show first 10
    print(f"  {t['name'][:50]}... - State: {t['state']}, Seeds: {t['num_seeds']}, Ratio: {t['ratio']:.2f}")

print()
print('=== TORRENTS ELIGIBLE FOR AUTO-MANAGEMENT ===')
eligible = []
for t in data:
    if (t['num_seeds'] >= config['seed_limit'] and 
        t['ratio'] >= config['min_ratio'] and 
        t['state'] in ['uploading', 'stalledUP', 'queuedUP']):
        eligible.append(t)

print(f"Found {len(eligible)} torrents eligible for auto-management:")
for t in eligible[:5]:  # Show first 5
    print(f"  {t['name'][:50]}... - State: {t['state']}, Seeds: {t['num_seeds']}, Ratio: {t['ratio']:.2f}")
