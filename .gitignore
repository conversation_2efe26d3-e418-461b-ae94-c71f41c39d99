# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/
.env

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
*.log
logs/
seed_manager.log

# Configuration (keep .example files)
.env
config.json

# OS
.DS_Store
Thumbs.db

# Docker
.dockerignore

# Testing
.coverage
htmlcov/
.pytest_cache/
.tox/
