#!/usr/bin/env python3

import qbittorrentapi

def check_qbt_settings():
    qbt = qbittorrentapi.Client(
        host='https://qbt.wikizell.com:443',
        username='admin',
        password='!st3al7h84!'
    )
    qbt.auth_log_in()
    
    prefs = qbt.app_preferences()
    print('=== QBITTORRENT QUEUE SETTINGS ===')
    print(f'Max active downloads: {prefs.get("max_active_downloads", "N/A")}')
    print(f'Max active uploads: {prefs.get("max_active_uploads", "N/A")}')
    print(f'Max active torrents: {prefs.get("max_active_torrents", "N/A")}')
    print(f'Queue enabled: {prefs.get("queueing_enabled", "N/A")}')
    print(f'Max ratio enabled: {prefs.get("max_ratio_enabled", "N/A")}')
    print(f'Max ratio: {prefs.get("max_ratio", "N/A")}')
    print(f'Max ratio action: {prefs.get("max_ratio_act", "N/A")}')
    print(f'Max seeding time enabled: {prefs.get("max_seeding_time_enabled", "N/A")}')
    print(f'Max seeding time: {prefs.get("max_seeding_time", "N/A")}')
    print()
    
    print('=== CURRENT TORRENT STATES ===')
    torrents = qbt.torrents_info()
    states = {}
    for t in torrents:
        state = t.state
        states[state] = states.get(state, 0) + 1
    
    for state, count in sorted(states.items()):
        print(f'{state}: {count}')
    
    print()
    print('=== SAMPLE QUEUED TORRENTS ===')
    queued_torrents = [t for t in torrents if t.state == 'queuedUP'][:5]
    for t in queued_torrents:
        print(f'Hash: {t.hash[:8]}... Name: {t.name[:50]}... State: {t.state} Seeds: {t.num_seeds} Ratio: {t.ratio:.2f}')

if __name__ == '__main__':
    check_qbt_settings()
