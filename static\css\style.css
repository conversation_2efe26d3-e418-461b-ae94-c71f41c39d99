/* Apple Human Interface Guidelines Color System */
:root {
    /* Light Mode Colors */
    --color-system-background: #ffffff;
    --color-secondary-system-background: #f2f2f7;
    --color-tertiary-system-background: #ffffff;
    --color-system-grouped-background: #f2f2f7;
    --color-secondary-system-grouped-background: #ffffff;
    --color-tertiary-system-grouped-background: #f2f2f7;

    /* Text Colors */
    --color-label: #000000;
    --color-secondary-label: #3c3c43;
    --color-tertiary-label: #3c3c4399;
    --color-quaternary-label: #3c3c432d;

    /* Fill Colors */
    --color-system-fill: #78788033;
    --color-secondary-system-fill: #78788028;
    --color-tertiary-system-fill: #7676801e;
    --color-quaternary-system-fill: #74748014;

    /* Accent Colors */
    --color-blue: #007aff;
    --color-green: #34c759;
    --color-indigo: #5856d6;
    --color-orange: #ff9500;
    --color-pink: #ff2d92;
    --color-purple: #af52de;
    --color-red: #ff3b30;
    --color-teal: #5ac8fa;
    --color-yellow: #ffcc00;

    /* Gray Colors */
    --color-gray: #8e8e93;
    --color-gray2: #aeaeb2;
    --color-gray3: #c7c7cc;
    --color-gray4: #d1d1d6;
    --color-gray5: #e5e5ea;
    --color-gray6: #f2f2f7;

    /* Separator Colors */
    --color-separator: #3c3c4349;
    --color-opaque-separator: #c6c6c8;
}

[data-theme="dark"] {
    /* Dark Mode Colors */
    --color-system-background: #000000;
    --color-secondary-system-background: #1c1c1e;
    --color-tertiary-system-background: #2c2c2e;
    --color-system-grouped-background: #000000;
    --color-secondary-system-grouped-background: #1c1c1e;
    --color-tertiary-system-grouped-background: #2c2c2e;

    /* Text Colors */
    --color-label: #ffffff;
    --color-secondary-label: #ebebf5;
    --color-tertiary-label: #ebebf599;
    --color-quaternary-label: #ebebf52d;

    /* Fill Colors */
    --color-system-fill: #78788033;
    --color-secondary-system-fill: #78788028;
    --color-tertiary-system-fill: #7676801e;
    --color-quaternary-system-fill: #74748014;

    /* Accent Colors (adjusted for dark mode) */
    --color-blue: #0a84ff;
    --color-green: #30d158;
    --color-indigo: #5e5ce6;
    --color-orange: #ff9f0a;
    --color-pink: #ff2d92;
    --color-purple: #bf5af2;
    --color-red: #ff453a;
    --color-teal: #64d2ff;
    --color-yellow: #ffd60a;

    /* Gray Colors */
    --color-gray: #8e8e93;
    --color-gray2: #636366;
    --color-gray3: #48484a;
    --color-gray4: #3a3a3c;
    --color-gray5: #2c2c2e;
    --color-gray6: #1c1c1e;

    /* Separator Colors */
    --color-separator: #54545899;
    --color-opaque-separator: #38383a;
}

/* Base Styles */
body {
    background-color: var(--color-system-background);
    color: var(--color-label);
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', Roboto, sans-serif;
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Navigation */
.navbar {
    background-color: var(--color-secondary-system-background) !important;
    border-bottom: 1px solid var(--color-separator);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.navbar-brand, .nav-link {
    color: var(--color-label) !important;
    transition: color 0.2s ease;
}

.navbar-brand:hover, .nav-link:hover {
    color: var(--color-blue) !important;
}

/* Buttons */
.btn-outline-light {
    border-color: var(--color-separator);
    color: var(--color-secondary-label);
    background-color: transparent;
}

.btn-outline-light:hover {
    background-color: var(--color-system-fill);
    border-color: var(--color-blue);
    color: var(--color-blue);
}

/* Cards */
.card {
    background-color: var(--color-secondary-system-grouped-background);
    border: 1px solid var(--color-separator);
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.card-header {
    background-color: var(--color-tertiary-system-grouped-background);
    border-bottom: 1px solid var(--color-separator);
    color: var(--color-label);
    font-weight: 600;
    border-radius: 12px 12px 0 0 !important;
}

/* Table Styles */
#torrentsTable {
    background-color: var(--color-secondary-system-grouped-background);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    font-size: 0.875rem;
}

#torrentsTable th {
    background-color: var(--color-tertiary-system-grouped-background) !important;
    color: var(--color-secondary-label);
    font-weight: 600;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 1px solid var(--color-separator);
    transition: background-color 0.2s ease;
    padding: 0.75rem 0.5rem;
}

#torrentsTable td {
    background-color: var(--color-secondary-system-grouped-background);
    color: var(--color-label);
    border-bottom: 1px solid var(--color-separator);
    vertical-align: middle;
    padding: 0.5rem;
    transition: background-color 0.2s ease;
}

#torrentsTable tbody tr:hover td {
    background-color: var(--color-tertiary-system-grouped-background);
}

#torrentsTable th[data-sort] {
    cursor: pointer;
    user-select: none;
}

#torrentsTable th[data-sort]:hover {
    background-color: var(--color-system-fill) !important;
}

.sort-icon {
    font-size: 0.7rem;
    margin-left: 4px;
    opacity: 0.6;
    color: var(--color-secondary-label);
    transition: opacity 0.2s ease, color 0.2s ease;
}

th[data-sort]:hover .sort-icon {
    opacity: 1;
    color: var(--color-blue);
}

/* Torrent State Colors using Apple HIG */
.torrent-state-uploading {
    color: var(--color-green);
    font-weight: 600;
}

.torrent-state-forcedUP {
    color: var(--color-green);
    font-weight: 600;
}

.torrent-state-pausedUP {
    color: var(--color-gray);
}

.torrent-state-stalledUP {
    color: var(--color-orange);
}

.torrent-state-stoppedUP {
    color: var(--color-red);
}

.torrent-state-checkingUP {
    color: var(--color-blue);
}

.torrent-state-downloading {
    color: var(--color-blue);
    font-weight: 600;
}

.torrent-state-pausedDL {
    color: var(--color-gray);
}

.torrent-state-error {
    color: var(--color-red);
    font-weight: 600;
}

.torrent-state-queuedUP,
.torrent-state-queuedDL {
    color: var(--color-teal);
}

/* Progress Bar Styling */
.progress {
    background-color: var(--color-quaternary-system-fill);
    border-radius: 6px;
    height: 20px;
    overflow: hidden;
}

.progress-bar {
    background-color: var(--color-green);
    transition: width 0.3s ease;
    border-radius: 6px;
}

.progress-bar-custom {
    height: 20px;
}

/* Form Controls */
.form-control, .form-select {
    background-color: var(--color-tertiary-system-grouped-background);
    border: 1px solid var(--color-separator);
    color: var(--color-label);
    border-radius: 8px;
    transition: all 0.2s ease;
}

.form-control:focus, .form-select:focus {
    background-color: var(--color-tertiary-system-grouped-background);
    border-color: var(--color-blue);
    color: var(--color-label);
    box-shadow: 0 0 0 0.25rem rgba(10, 132, 255, 0.25);
}

/* Badges */
.badge {
    border-radius: 6px;
    font-weight: 500;
    padding: 0.375rem 0.75rem;
}

.badge.bg-success {
    background-color: var(--color-green) !important;
}

.badge.bg-warning {
    background-color: var(--color-orange) !important;
}

.badge.bg-danger {
    background-color: var(--color-red) !important;
}

.badge.bg-secondary {
    background-color: var(--color-gray) !important;
}

.badge.bg-primary {
    background-color: var(--color-blue) !important;
}

/* Buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: var(--color-blue);
    border-color: var(--color-blue);
}

.btn-primary:hover {
    background-color: var(--color-blue);
    border-color: var(--color-blue);
    opacity: 0.8;
}

.btn-success {
    background-color: var(--color-green);
    border-color: var(--color-green);
}

.btn-success:hover {
    background-color: var(--color-green);
    border-color: var(--color-green);
    opacity: 0.8;
}

.btn-warning {
    background-color: var(--color-orange);
    border-color: var(--color-orange);
}

.btn-warning:hover {
    background-color: var(--color-orange);
    border-color: var(--color-orange);
    opacity: 0.8;
}

.btn-danger {
    background-color: var(--color-red);
    border-color: var(--color-red);
}

.btn-danger:hover {
    background-color: var(--color-red);
    border-color: var(--color-red);
    opacity: 0.8;
}

/* Status Indicators */
.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 0.5rem;
}

.status-connected {
    background-color: var(--color-green);
    box-shadow: 0 0 0 2px rgba(52, 199, 89, 0.3);
}

.status-disconnected {
    background-color: var(--color-red);
    box-shadow: 0 0 0 2px rgba(255, 59, 48, 0.3);
}

/* Ratio Badges */
.ratio-excellent {
    background-color: var(--color-green) !important;
    color: white;
}

.ratio-good {
    background-color: var(--color-orange) !important;
    color: white;
}

.ratio-poor {
    background-color: var(--color-red) !important;
    color: white;
}

/* Upload Speed Highlighting */
.upload-active {
    color: var(--color-green);
    font-weight: 600;
}

/* Force Start Indicator */
.force-start-indicator {
    color: var(--color-blue);
    font-weight: 600;
}

/* Filter Panel */
.filter-panel {
    background-color: var(--color-tertiary-system-grouped-background);
    border: 1px solid var(--color-separator);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1rem;
}

/* Collapsible Content */
.collapse {
    transition: height 0.3s ease;
}

/* Modal */
.modal-content {
    background-color: var(--color-secondary-system-grouped-background);
    border: 1px solid var(--color-separator);
    border-radius: 12px;
    color: var(--color-label);
}

.modal-header {
    border-bottom: 1px solid var(--color-separator);
}

.modal-footer {
    border-top: 1px solid var(--color-separator);
}

/* Dashboard Metric Cards with Different Colors */
.metric-card:nth-child(1) {
    background: linear-gradient(135deg, var(--color-blue) 0%, var(--color-indigo) 100%);
    box-shadow: 0 4px 12px rgba(0, 122, 255, 0.2);
}

.metric-card:nth-child(2) {
    background: linear-gradient(135deg, var(--color-green) 0%, var(--color-teal) 100%);
    box-shadow: 0 4px 12px rgba(52, 199, 89, 0.2);
}

.metric-card:nth-child(3) {
    background: linear-gradient(135deg, var(--color-orange) 0%, var(--color-yellow) 100%);
    box-shadow: 0 4px 12px rgba(255, 149, 0, 0.2);
}

.metric-card:nth-child(4) {
    background: linear-gradient(135deg, var(--color-purple) 0%, var(--color-pink) 100%);
    box-shadow: 0 4px 12px rgba(175, 82, 222, 0.2);
}

/* Smooth transitions for theme changes */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Ensure proper text contrast in dark mode */
[data-theme="dark"] .metric-card {
    color: white;
}

[data-theme="dark"] .chart-container canvas {
    filter: brightness(0.9);
}
