/* Custom styles for the seed manager */
.progress-bar-custom {
    height: 20px;
}

/* qBittorrent-like table styling */
#torrentsTable {
    font-size: 0.875rem;
}

#torrentsTable td {
    vertical-align: middle;
    padding: 0.375rem 0.5rem;
}

#torrentsTable th {
    font-weight: 600;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Torrent state colors matching qBittorrent */
.torrent-state-uploading {
    color: #28a745;
    font-weight: bold;
}

.torrent-state-forcedUP {
    color: #28a745;
    font-weight: bold;
}

.torrent-state-pausedUP {
    color: #6c757d;
}

.torrent-state-stalledUP {
    color: #ffc107;
}

.torrent-state-stoppedUP {
    color: #dc3545;
}

.torrent-state-checkingUP {
    color: #17a2b8;
}

.torrent-state-downloading {
    color: #007bff;
    font-weight: bold;
}

.torrent-state-pausedDL {
    color: #6c757d;
}

.torrent-state-error {
    color: #dc3545;
    font-weight: bold;
}

.torrent-state-queuedUP,
.torrent-state-queuedDL {
    color: #17a2b8;
}

/* Progress bar styling */
.progress {
    background-color: #e9ecef;
    border-radius: 2px;
}

.progress-bar {
    background-color: #28a745;
    transition: width 0.3s ease;
}

.card-header {
    background-color: var(--bs-light);
}

.dark-mode .card-header {
    background-color: var(--bs-dark);
}

.dark-mode .table {
    --bs-table-bg: var(--bs-dark);
    --bs-table-color: var(--bs-light);
}

.dark-mode .table-dark {
    --bs-table-bg: #1a1a1a;
    --bs-table-color: var(--bs-light);
}

.dark-mode .card {
    background-color: var(--bs-dark);
    border-color: #444;
}

.dark-mode .modal-content {
    background-color: var(--bs-dark);
    color: var(--bs-light);
}

.dark-mode .form-control,
.dark-mode .form-select {
    background-color: #2d2d2d;
    border-color: #444;
    color: var(--bs-light);
}

.dark-mode .form-control:focus,
.dark-mode .form-select:focus {
    background-color: #2d2d2d;
    border-color: #0d6efd;
    color: var(--bs-light);
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}
