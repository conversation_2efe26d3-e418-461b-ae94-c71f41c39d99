#!/usr/bin/env python3
"""
qBittorrent Seed Manager
A web application for managing seeding behavior in qBittorrent
"""

import os
import json
import logging
import threading
import time
from datetime import datetime
from typing import Dict, List, Optional

from flask import Flask, render_template, jsonify, request
from flask_cors import CORS
import qbittorrentapi
from dotenv import load_dotenv
import schedule

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('seed_manager.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

class SeedManager:
    def __init__(self):
        self.config = self.load_config()
        self.qb_client = None
        self.last_check = None
        self.stats = {
            'total_torrents': 0,
            'paused_torrents': 0,
            'seeding_torrents': 0,
            'avg_ratio': 0.0,
            'total_peers': 0,
            'total_seeders': 0
        }
        self.torrent_history = []
        self.activity_log = []  # Store recent management activities

    def load_config(self) -> Dict:
        """Load configuration from config.json and environment variables"""
        config = {}
        
        # Try to load from config.json first
        if os.path.exists('config.json'):
            with open('config.json', 'r') as f:
                config = json.load(f)
        
        # Override with environment variables if they exist
        config.update({
            'qb_host': os.getenv('QB_HOST', config.get('qb_host', 'localhost')),
            'qb_port': int(os.getenv('QB_PORT', config.get('qb_port', 8080))),
            'qb_username': os.getenv('QB_USERNAME', config.get('qb_username', 'admin')),
            'qb_password': os.getenv('QB_PASSWORD', config.get('qb_password', 'adminpass')),
            'seed_limit': int(os.getenv('SEED_LIMIT', config.get('seed_limit', 5))),
            'min_ratio': float(os.getenv('MIN_RATIO', config.get('min_ratio', 2.0))),
            'category': os.getenv('CATEGORY', config.get('category', 'movies')),
            'check_interval': int(os.getenv('CHECK_INTERVAL', config.get('check_interval', 300))),
            'action_type': os.getenv('ACTION_TYPE', config.get('action_type', 'stop'))  # 'stop' or 'pause'
        })
        
        return config
    
    def connect_qbittorrent(self) -> bool:
        """Connect to qBittorrent Web API"""
        try:
            # Determine if we should use HTTPS based on port
            use_https = self.config['qb_port'] == 443

            # Build the URL with proper protocol
            protocol = 'https' if use_https else 'http'
            url = f"{protocol}://{self.config['qb_host']}:{self.config['qb_port']}"

            self.qb_client = qbittorrentapi.Client(
                host=url,
                username=self.config['qb_username'],
                password=self.config['qb_password'],
                VERIFY_WEBUI_CERTIFICATE=False  # Allow self-signed certificates
            )
            
            # Test connection
            self.qb_client.auth_log_in()
            logger.info(f"Connected to qBittorrent at {self.config['qb_host']}:{self.config['qb_port']}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to qBittorrent: {e}")
            self.qb_client = None
            return False
    
    def get_torrents_by_category(self) -> List[Dict]:
        """Get all torrents in the specified category"""
        if not self.qb_client:
            return []

        try:
            torrents = self.qb_client.torrents_info(category=self.config['category'])
            torrent_dicts = [self._torrent_to_dict(torrent) for torrent in torrents]

            # Update statistics whenever we fetch torrents
            self._update_stats(torrent_dicts)

            return torrent_dicts
        except Exception as e:
            logger.error(f"Failed to get torrents: {e}")
            return []
    
    def _torrent_to_dict(self, torrent) -> Dict:
        """Convert qBittorrent torrent object to dictionary"""
        # Get all available peer/seed related fields
        num_seeds = getattr(torrent, 'num_seeds', 0)
        num_leechs = getattr(torrent, 'num_leechs', 0)

        # Try alternative field names - qBittorrent API field variations
        if num_seeds == 0:
            # Try different possible field names for seeds
            num_seeds = getattr(torrent, 'seeds_total', 0) or \
                       getattr(torrent, 'num_complete', 0) or \
                       getattr(torrent, 'complete', 0)

        if num_leechs == 0:
            # Try different possible field names for peers/leechers
            num_leechs = getattr(torrent, 'peers_total', 0) or \
                        getattr(torrent, 'num_incomplete', 0) or \
                        getattr(torrent, 'incomplete', 0)



        return {
            'hash': torrent.hash,
            'name': torrent.name,
            'size': torrent.size,
            'progress': torrent.progress,
            'ratio': torrent.ratio,
            'num_seeds': num_seeds,
            'num_leechs': num_leechs,
            'state': torrent.state,
            'category': getattr(torrent, 'category', ''),
            'priority': getattr(torrent, 'priority', 0),
            'downloaded': getattr(torrent, 'downloaded', 0),
            'uploaded': getattr(torrent, 'uploaded', 0),
            'dlspeed': getattr(torrent, 'dlspeed', 0),
            'upspeed': getattr(torrent, 'upspeed', 0),
            'eta': getattr(torrent, 'eta', 0),
            'added_on': getattr(torrent, 'added_on', 0),
            'completion_on': getattr(torrent, 'completion_on', 0),
            'force_start': getattr(torrent, 'force_start', False),
            # Additional useful fields
            'tracker': getattr(torrent, 'tracker', ''),
            'tags': getattr(torrent, 'tags', ''),
            'time_active': getattr(torrent, 'time_active', 0),
            'seeding_time': getattr(torrent, 'seeding_time', 0)
        }

    def check_and_manage_torrents(self):
        """Main logic to check and manage torrents based on seeding criteria"""
        if not self.qb_client:
            if not self.connect_qbittorrent():
                return

        try:
            # Step 1: Get initial torrent list
            initial_torrents = self.get_torrents_by_category()
            self.last_check = datetime.now()

            # Step 2: Identify torrents that need to be temporarily started for accurate seed counts
            torrents_to_start = []
            force_started_hashes = set()
            gundam_gquuuuuux_found = False
            gundam_gquuuuuux_info = None

            for torrent in initial_torrents:
                # Special tracking for Mobile Suit Gundam GQuuuuuuX
                if 'gundam gquuuuuux' in torrent['name'].lower():
                    gundam_gquuuuuux_found = True
                    gundam_gquuuuuux_info = torrent
                    logger.info(f"[GUNDAM] GQUUUUUUX FOUND: {torrent['name']}")
                    logger.info(f"[GUNDAM] Current state: {torrent['state']}")
                    logger.info(f"[GUNDAM] Current ratio: {torrent['ratio']:.2f}")
                    logger.info(f"[GUNDAM] Force start: {torrent.get('force_start', False)}")
                    logger.info(f"[GUNDAM] Min ratio threshold: {self.config['min_ratio']}")

                # Track force-started torrents to avoid pausing them
                if torrent.get('force_start', False):
                    force_started_hashes.add(torrent['hash'])

                # Identify completed/paused torrents that need temporary starting for accurate seed counts
                if (torrent['state'] in ['pausedUP', 'stalledUP', 'queuedUP', 'stoppedUP'] and
                    not torrent.get('force_start', False) and
                    torrent['ratio'] >= self.config['min_ratio']):  # Only check torrents that might qualify
                    torrents_to_start.append(torrent['hash'])

                    # Special logging for Gundam GQuuuuuuX
                    if 'gundam gquuuuuux' in torrent['name'].lower():
                        logger.info(f"[GUNDAM] GQUUUUUUX QUALIFIES for temporary start!")

            # Log if Gundam GQuuuuuuX was not found
            if not gundam_gquuuuuux_found:
                logger.info("[GUNDAM] GQUUUUUUX NOT FOUND in current torrent list")
            elif gundam_gquuuuuux_info and gundam_gquuuuuux_info['hash'] not in torrents_to_start:
                logger.info(f"[GUNDAM] GQUUUUUUX DOES NOT QUALIFY for temporary start")
                logger.info(f"[GUNDAM] Reason: State='{gundam_gquuuuuux_info['state']}', Ratio={gundam_gquuuuuux_info['ratio']:.2f}, ForceStart={gundam_gquuuuuux_info.get('force_start', False)}")

            # Step 3: Temporarily start completed torrents to get accurate tracker data
            if torrents_to_start:
                logger.info(f"Temporarily starting {len(torrents_to_start)} torrents to get accurate seed counts")

                # Log which torrents are being started
                for torrent in initial_torrents:
                    if torrent['hash'] in torrents_to_start:
                        logger.info(f"Starting torrent: {torrent['name'][:50]}... (State: {torrent['state']} -> uploading)")
                        self.log_activity('Temp Started', torrent['name'], f"For seed count check (was {torrent['state']})")

                # Start all qualifying torrents at once
                for torrent_hash in torrents_to_start:
                    try:
                        # Check if this is the Gundam GQuuuuuuX torrent
                        is_gundam_gquuuuuux = False
                        if gundam_gquuuuuux_info and torrent_hash == gundam_gquuuuuux_info['hash']:
                            is_gundam_gquuuuuux = True
                            logger.info(f"[GUNDAM] STARTING GQUUUUUUX FORCE START PROCESS!")

                        # First, get current state
                        current_info = self.qb_client.torrents_info(torrent_hashes=torrent_hash)
                        if current_info:
                            current_state = current_info[0].state
                            if is_gundam_gquuuuuux:
                                logger.info(f"[GUNDAM] GQUUUUUUX Before force start: state = {current_state}")
                            else:
                                logger.info(f"Before resume: {torrent_hash[:8]}... state = {current_state}")

                        # Force start the torrent to ensure it becomes active
                        self.qb_client.torrents_set_force_start(torrent_hashes=torrent_hash, value=True)
                        if is_gundam_gquuuuuux:
                            logger.info(f"[GUNDAM] GQUUUUUUX Force start command sent!")
                        else:
                            logger.info(f"Force started torrent {torrent_hash[:8]}... for seed count check")

                        # Wait a moment and check new state
                        time.sleep(1)
                        new_info = self.qb_client.torrents_info(torrent_hashes=torrent_hash)
                        if new_info:
                            new_state = new_info[0].state
                            if is_gundam_gquuuuuux:
                                logger.info(f"[GUNDAM] GQUUUUUUX After force start: state = {new_state}")
                            else:
                                logger.info(f"After resume: {torrent_hash[:8]}... state = {new_state}")

                    except Exception as e:
                        logger.warning(f"Failed to resume torrent {torrent_hash}: {e}")

                # Wait for tracker updates (allow time for torrents to connect and get peer info)
                logger.info("Waiting 15 seconds for tracker updates...")
                time.sleep(15)

                # Log completion of tracker update phase and verify final states
                logger.info("Tracker update phase completed, verifying final states...")
                for torrent_hash in torrents_to_start:
                    try:
                        # Check if this is the Gundam GQuuuuuuX torrent
                        is_gundam_gquuuuuux = False
                        if gundam_gquuuuuux_info and torrent_hash == gundam_gquuuuuux_info['hash']:
                            is_gundam_gquuuuuux = True

                        final_info = self.qb_client.torrents_info(torrent_hashes=torrent_hash)
                        if final_info:
                            final_state = final_info[0].state
                            if is_gundam_gquuuuuux:
                                logger.info(f"[GUNDAM] GQUUUUUUX Final state after 15 seconds: {final_state}")
                            else:
                                logger.info(f"Final state: {torrent_hash[:8]}... = {final_state}")

                        # Remove force start flag to return torrent to normal queue management
                        self.qb_client.torrents_set_force_start(torrent_hashes=torrent_hash, value=False)
                        if is_gundam_gquuuuuux:
                            logger.info(f"[GUNDAM] GQUUUUUUX Force start flag removed - returning to normal queue management")
                        else:
                            logger.info(f"Removed force start flag from {torrent_hash[:8]}...")

                    except Exception as e:
                        logger.warning(f"Failed to check final state for {torrent_hash}: {e}")

            # Step 4: Get updated torrent info with fresh seed/peer counts
            updated_torrents = self.get_torrents_by_category()

            # Step 5: Apply management logic with accurate data
            paused_count = 0
            seeding_count = 0

            for torrent in updated_torrents:
                # Count states
                if torrent['state'] in ['pausedUP', 'pausedDL']:
                    paused_count += 1
                elif torrent['state'] in ['uploading', 'stalledUP', 'queuedUP', 'forcedUP']:
                    seeding_count += 1

                    # Skip force-started torrents
                    if torrent['hash'] in force_started_hashes:
                        continue

                    # Check if torrent meets criteria for auto-management
                    actual_seeds = torrent.get('num_seeds', 0)

                    # STOP/PAUSE torrents with high seeds AND high ratio
                    if (actual_seeds >= self.config['seed_limit'] and
                        torrent['ratio'] >= self.config['min_ratio'] and
                        torrent['state'] in ['uploading', 'stalledUP', 'queuedUP']):  # Manage active and queued torrents

                        # Use configurable action type
                        action_type = self.config.get('action_type', 'stop')
                        if action_type == 'pause':
                            self._pause_torrent(torrent['hash'])
                            action_name = 'Auto Paused'
                            state_change = 'uploading -> pausedUP'
                            log_message = f"Auto-paused torrent: {torrent['name']}"
                        else:  # default to 'stop'
                            self._stop_torrent(torrent['hash'])
                            action_name = 'Auto Stopped'
                            state_change = 'uploading -> stoppedUP'
                            log_message = f"Auto-stopped torrent: {torrent['name']}"

                        self.log_activity(action_name, torrent['name'],
                                        f"Seeds: {actual_seeds}, Ratio: {torrent['ratio']:.2f} ({state_change})")
                        logger.info(f"{log_message} "
                                  f"(Seeds: {actual_seeds}, Ratio: {torrent['ratio']:.2f})")
                        paused_count += 1
                        seeding_count -= 1

                    # RESUME/START torrents with low seeds (regardless of ratio)
                    elif (actual_seeds < self.config['seed_limit'] and
                          torrent['progress'] >= 1.0 and  # Only completed torrents
                          torrent['state'] in ['queuedUP', 'stoppedUP', 'pausedUP']):  # Not actively seeding

                        self._resume_torrent(torrent['hash'])
                        action_name = 'Auto Resumed'
                        state_change = f"{torrent['state']} -> uploading"
                        log_message = f"Auto-resumed torrent: {torrent['name']}"

                        self.log_activity(action_name, torrent['name'],
                                        f"Seeds: {actual_seeds}, Ratio: {torrent['ratio']:.2f} ({state_change})")
                        logger.info(f"{log_message} "
                                  f"(Seeds: {actual_seeds}, Ratio: {torrent['ratio']:.2f})")
                        seeding_count += 1

            # Stats are already updated by get_torrents_by_category()
            action_type = self.config.get('action_type', 'stop')
            action_name = 'stopped' if action_type == 'stop' else 'paused'
            logger.info(f"Checked {len(updated_torrents)} torrents. "
                       f"Auto-{action_name}: {paused_count}, Currently seeding: {seeding_count}")

        except Exception as e:
            logger.error(f"Error during torrent check: {e}")

    def get_fresh_seed_count(self, torrent_hash: str) -> int:
        """Get accurate seed count for a specific torrent by temporarily starting it if needed"""
        try:
            # Get current torrent info
            torrent_info = self.qb_client.torrents_info(torrent_hashes=torrent_hash)
            if not torrent_info:
                return 0

            torrent = torrent_info[0]
            current_state = torrent.state

            # If torrent is not active, temporarily start it to get fresh tracker data
            if current_state in ['pausedUP', 'stalledUP', 'queuedUP']:
                logger.debug(f"Temporarily starting torrent {torrent_hash[:8]}... to get fresh seed count")
                self.qb_client.torrents_resume(torrent_hash)

                # Wait for tracker update
                time.sleep(8)

                # Get updated info
                updated_info = self.qb_client.torrents_info(torrent_hashes=torrent_hash)
                if updated_info:
                    updated_torrent = updated_info[0]
                    torrent_dict = self._torrent_to_dict(updated_torrent)
                    fresh_seed_count = torrent_dict['num_seeds']

                    # Return to original state if it was paused
                    if current_state in ['pausedUP']:
                        self.qb_client.torrents_pause(torrent_hash)

                    return fresh_seed_count

            # If already active, use current data
            torrent_dict = self._torrent_to_dict(torrent)
            return torrent_dict['num_seeds']

        except Exception as e:
            logger.error(f"Failed to get fresh seed count for {torrent_hash}: {e}")
            return 0

    def _stop_torrent(self, torrent_hash: str):
        """Stop a specific torrent completely"""
        try:
            self.qb_client.torrents_stop(torrent_hashes=torrent_hash)
        except Exception as e:
            logger.error(f"Failed to stop torrent {torrent_hash}: {e}")

    def _pause_torrent(self, torrent_hash: str):
        """Pause a specific torrent"""
        try:
            self.qb_client.torrents_pause(torrent_hashes=torrent_hash)
        except Exception as e:
            logger.error(f"Failed to pause torrent {torrent_hash}: {e}")

    def _resume_torrent(self, torrent_hash: str):
        """Resume a specific torrent"""
        try:
            self.qb_client.torrents_resume(torrent_hashes=torrent_hash)
        except Exception as e:
            logger.error(f"Failed to resume torrent {torrent_hash}: {e}")

    def force_check_torrent(self, torrent_hash: str):
        """Force recheck a specific torrent"""
        try:
            self.qb_client.torrents_recheck(torrent_hashes=torrent_hash)
            logger.info(f"Force checking torrent: {torrent_hash}")
        except Exception as e:
            logger.error(f"Failed to force check torrent {torrent_hash}: {e}")

    def force_start_torrent(self, torrent_hash: str):
        """Force start a specific torrent (bypass queue)"""
        try:
            self.qb_client.torrents_set_force_start(torrent_hashes=torrent_hash, value=True)
            logger.info(f"Force starting torrent: {torrent_hash}")
        except Exception as e:
            logger.error(f"Failed to force start torrent {torrent_hash}: {e}")

    def update_config(self, new_config: Dict):
        """Update configuration settings"""
        self.config.update(new_config)

        # Save to config.json
        with open('config.json', 'w') as f:
            json.dump(self.config, f, indent=2)

        logger.info("Configuration updated")

    def log_activity(self, action: str, torrent_name: str, details: str = ""):
        """Log management activity"""
        activity = {
            'timestamp': datetime.now().isoformat(),
            'action': action,
            'torrent_name': torrent_name,
            'details': details
        }
        self.activity_log.insert(0, activity)  # Add to beginning

        # Keep only last 50 activities
        if len(self.activity_log) > 50:
            self.activity_log = self.activity_log[:50]

    def _update_stats(self, torrents: List[Dict]):
        """Update statistics from torrent data"""
        if not torrents:
            self.stats = {
                'total_torrents': 0,
                'paused_torrents': 0,
                'seeding_torrents': 0,
                'avg_ratio': 0.0,
                'total_peers': 0,
                'total_seeders': 0
            }
            return

        total_ratio = 0
        total_peers = 0
        total_seeders = 0
        paused_count = 0
        seeding_count = 0

        for torrent in torrents:
            total_ratio += torrent.get('ratio', 0)
            total_peers += torrent.get('num_leechs', 0)
            total_seeders += torrent.get('num_seeds', 0)

            # Count different states
            state = torrent.get('state', '')
            if state in ['pausedUP', 'pausedDL']:
                paused_count += 1
            elif state in ['uploading', 'stalledUP', 'queuedUP', 'forcedUP']:
                seeding_count += 1

        # Update stats
        self.stats.update({
            'total_torrents': len(torrents),
            'paused_torrents': paused_count,
            'seeding_torrents': seeding_count,
            'avg_ratio': total_ratio / len(torrents) if torrents else 0,
            'total_peers': total_peers,
            'total_seeders': total_seeders
        })

# Global seed manager instance
seed_manager = SeedManager()

# Flask Routes
@app.route('/')
def index():
    """Serve the main page"""
    return render_template('index.html')

@app.route('/dashboard')
def dashboard():
    """Serve the dashboard page"""
    return render_template('dashboard.html')

@app.route('/info')
def info():
    """Serve the info/documentation page"""
    return render_template('info.html')

@app.route('/favicon.ico')
def favicon():
    """Return a simple favicon response"""
    return '', 204  # No content response

@app.route('/api/status')
def api_status():
    """Get current application status"""
    return jsonify({
        'connected': seed_manager.qb_client is not None,
        'last_check': seed_manager.last_check.isoformat() if seed_manager.last_check else None,
        'config': seed_manager.config,
        'stats': seed_manager.stats
    })

@app.route('/api/torrents')
def api_torrents():
    """Get all torrents in the configured category"""
    torrents = seed_manager.get_torrents_by_category()
    return jsonify(torrents)

@app.route('/api/connect', methods=['POST'])
def api_connect():
    """Test connection to qBittorrent"""
    success = seed_manager.connect_qbittorrent()
    return jsonify({'success': success})

@app.route('/api/check', methods=['POST'])
def api_check():
    """Manually trigger torrent check"""
    seed_manager.check_and_manage_torrents()
    return jsonify({'success': True, 'message': 'Check completed'})

@app.route('/api/torrent/<torrent_hash>/resume', methods=['POST'])
def api_resume_torrent(torrent_hash):
    """Resume a specific torrent"""
    # Get torrent name for logging
    torrents = seed_manager.get_torrents_by_category()
    torrent_name = next((t['name'] for t in torrents if t['hash'] == torrent_hash), 'Unknown')

    seed_manager._resume_torrent(torrent_hash)
    seed_manager.log_activity('Resumed', torrent_name, 'Manual resume')
    return jsonify({'success': True, 'message': 'Torrent resumed'})

@app.route('/api/torrent/<torrent_hash>/stop', methods=['POST'])
def api_stop_torrent(torrent_hash):
    """Stop a specific torrent"""
    # Get torrent name for logging
    torrents = seed_manager.get_torrents_by_category()
    torrent_name = next((t['name'] for t in torrents if t['hash'] == torrent_hash), 'Unknown')

    seed_manager._stop_torrent(torrent_hash)
    seed_manager.log_activity('Stopped', torrent_name, 'Manual stop')
    return jsonify({'success': True, 'message': 'Torrent stopped'})

@app.route('/api/torrent/<torrent_hash>/pause', methods=['POST'])
def api_pause_torrent(torrent_hash):
    """Pause a specific torrent"""
    # Get torrent name for logging
    torrents = seed_manager.get_torrents_by_category()
    torrent_name = next((t['name'] for t in torrents if t['hash'] == torrent_hash), 'Unknown')

    seed_manager._pause_torrent(torrent_hash)
    seed_manager.log_activity('Paused', torrent_name, 'Manual pause')
    return jsonify({'success': True, 'message': 'Torrent paused'})

@app.route('/api/torrent/<torrent_hash>/force-check', methods=['POST'])
def api_force_check_torrent(torrent_hash):
    """Force check a specific torrent"""
    seed_manager.force_check_torrent(torrent_hash)
    return jsonify({'success': True, 'message': 'Force check initiated'})

@app.route('/api/torrent/<torrent_hash>/force-start', methods=['POST'])
def api_force_start_torrent(torrent_hash):
    """Force start a specific torrent"""
    # Get torrent name for logging
    torrents = seed_manager.get_torrents_by_category()
    torrent_name = next((t['name'] for t in torrents if t['hash'] == torrent_hash), 'Unknown')

    seed_manager.force_start_torrent(torrent_hash)
    seed_manager.log_activity('Force Started', torrent_name, 'Manual force start')
    return jsonify({'success': True, 'message': 'Force start initiated'})

@app.route('/api/config', methods=['GET', 'POST'])
def api_config():
    """Get or update configuration"""
    if request.method == 'GET':
        return jsonify(seed_manager.config)

    elif request.method == 'POST':
        try:
            new_config = request.get_json()
            if not new_config:
                return jsonify({'success': False, 'message': 'No JSON data provided'}), 400

            seed_manager.update_config(new_config)
            return jsonify({'success': True, 'message': 'Configuration updated'})
        except Exception as e:
            logger.error(f"Failed to update config: {e}")
            return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/queue-settings', methods=['GET', 'POST'])
def api_queue_settings():
    """Get or update qBittorrent queue settings"""
    if request.method == 'POST':
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'message': 'No JSON data provided'}), 400

            # Apply queue settings to qBittorrent
            prefs = {}

            if 'queueing_enabled' in data:
                prefs['queueing_enabled'] = data['queueing_enabled']

            if 'max_active_uploads' in data:
                prefs['max_active_uploads'] = data['max_active_uploads']

            if 'max_active_torrents' in data:
                prefs['max_active_torrents'] = data['max_active_torrents']

            if 'max_active_downloads' in data:
                prefs['max_active_downloads'] = data['max_active_downloads']

            seed_manager.qb_client.app_set_preferences(prefs)
            logger.info(f"Updated qBittorrent queue settings: {prefs}")

            return jsonify({'success': True, 'message': 'Queue settings updated successfully'})
        except Exception as e:
            logger.error(f"Error updating queue settings: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    # GET request - return current settings with suggestions
    try:
        prefs = seed_manager.qb_client.app_preferences()
        torrents = seed_manager.qb_client.torrents_info()

        # Calculate suggestions based on current torrents
        total_torrents = len(torrents)
        completed_torrents = len([t for t in torrents if t.progress >= 1.0])
        downloading_torrents = len([t for t in torrents if t.progress < 1.0])

        # Smart suggestions
        suggested_max_uploads = min(max(completed_torrents // 2, 20), 100)  # 50% of completed, min 20, max 100
        suggested_max_torrents = min(max(total_torrents // 2, 50), 200)     # 50% of total, min 50, max 200
        suggested_max_downloads = min(max(downloading_torrents + 10, 20), 50)  # Current + buffer, min 20, max 50

        current_settings = {
            'queueing_enabled': prefs.get('queueing_enabled', True),
            'max_active_uploads': prefs.get('max_active_uploads', 10),
            'max_active_torrents': prefs.get('max_active_torrents', 10),
            'max_active_downloads': prefs.get('max_active_downloads', 100)
        }

        suggestions = {
            'total_torrents': total_torrents,
            'completed_torrents': completed_torrents,
            'downloading_torrents': downloading_torrents,
            'suggested_max_uploads': suggested_max_uploads,
            'suggested_max_torrents': suggested_max_torrents,
            'suggested_max_downloads': suggested_max_downloads,
            'disable_queue_option': True  # Always show option to disable
        }

        return jsonify({
            'current': current_settings,
            'suggestions': suggestions
        })
    except Exception as e:
        logger.error(f"Error getting queue settings: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/categories')
def api_categories():
    """Get all available categories from qBittorrent"""
    if not seed_manager.qb_client:
        return jsonify({'success': False, 'message': 'Not connected to qBittorrent'})

    try:
        categories = seed_manager.qb_client.torrents_categories()
        category_list = list(categories.keys()) if categories else []

        # Also get categories from existing torrents
        torrents = seed_manager.qb_client.torrents_info()
        torrent_categories = set()
        for torrent in torrents:
            if torrent.category and torrent.category.strip():
                torrent_categories.add(torrent.category)

        # Combine both lists and remove duplicates
        all_categories = list(set(category_list + list(torrent_categories)))
        all_categories.sort()

        return jsonify({
            'success': True,
            'categories': all_categories,
            'current': seed_manager.config['category']
        })
    except Exception as e:
        logger.error(f"Failed to get categories: {e}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/activity')
def api_activity():
    """Get recent management activity"""
    return jsonify({
        'success': True,
        'activities': seed_manager.activity_log
    })

@app.route('/logs')
def logs():
    """Serve the logs page"""
    return render_template('logs.html')

def run_scheduler():
    """Run the scheduled torrent checks in a separate thread"""
    schedule.every(seed_manager.config['check_interval']).seconds.do(
        seed_manager.check_and_manage_torrents
    )

    while True:
        schedule.run_pending()
        time.sleep(1)

if __name__ == '__main__':
    # Connect to qBittorrent on startup
    seed_manager.connect_qbittorrent()

    # Start scheduler in background thread
    scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
    scheduler_thread.start()

    app.run(debug=True, host='0.0.0.0', port=5000)
