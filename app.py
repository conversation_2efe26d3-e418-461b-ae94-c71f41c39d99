#!/usr/bin/env python3
"""
qBittorrent Seed Manager
A web application for managing seeding behavior in qBittorrent
"""

import os
import json
import logging
import threading
import time
from datetime import datetime
from typing import Dict, List, Optional

from flask import Flask, render_template, jsonify, request
from flask_cors import CORS
import qbittorrentapi
from dotenv import load_dotenv
import schedule

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('seed_manager.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

class SeedManager:
    def __init__(self):
        self.config = self.load_config()
        self.qb_client = None
        self.last_check = None
        self.stats = {
            'total_torrents': 0,
            'paused_torrents': 0,
            'seeding_torrents': 0,
            'avg_ratio': 0.0,
            'total_peers': 0,
            'total_seeders': 0
        }
        self.torrent_history = []
        
    def load_config(self) -> Dict:
        """Load configuration from config.json and environment variables"""
        config = {}
        
        # Try to load from config.json first
        if os.path.exists('config.json'):
            with open('config.json', 'r') as f:
                config = json.load(f)
        
        # Override with environment variables if they exist
        config.update({
            'qb_host': os.getenv('QB_HOST', config.get('qb_host', 'localhost')),
            'qb_port': int(os.getenv('QB_PORT', config.get('qb_port', 8080))),
            'qb_username': os.getenv('QB_USERNAME', config.get('qb_username', 'admin')),
            'qb_password': os.getenv('QB_PASSWORD', config.get('qb_password', 'adminpass')),
            'seed_limit': int(os.getenv('SEED_LIMIT', config.get('seed_limit', 5))),
            'min_ratio': float(os.getenv('MIN_RATIO', config.get('min_ratio', 2.0))),
            'category': os.getenv('CATEGORY', config.get('category', 'movies')),
            'check_interval': int(os.getenv('CHECK_INTERVAL', config.get('check_interval', 300)))
        })
        
        return config
    
    def connect_qbittorrent(self) -> bool:
        """Connect to qBittorrent Web API"""
        try:
            # Determine if we should use HTTPS based on port
            use_https = self.config['qb_port'] == 443

            # Build the URL with proper protocol
            protocol = 'https' if use_https else 'http'
            url = f"{protocol}://{self.config['qb_host']}:{self.config['qb_port']}"

            self.qb_client = qbittorrentapi.Client(
                host=url,
                username=self.config['qb_username'],
                password=self.config['qb_password'],
                VERIFY_WEBUI_CERTIFICATE=False  # Allow self-signed certificates
            )
            
            # Test connection
            self.qb_client.auth_log_in()
            logger.info(f"Connected to qBittorrent at {self.config['qb_host']}:{self.config['qb_port']}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to qBittorrent: {e}")
            self.qb_client = None
            return False
    
    def get_torrents_by_category(self) -> List[Dict]:
        """Get all torrents in the specified category"""
        if not self.qb_client:
            return []
        
        try:
            torrents = self.qb_client.torrents_info(category=self.config['category'])
            return [self._torrent_to_dict(torrent) for torrent in torrents]
        except Exception as e:
            logger.error(f"Failed to get torrents: {e}")
            return []
    
    def _torrent_to_dict(self, torrent) -> Dict:
        """Convert qBittorrent torrent object to dictionary"""
        return {
            'hash': torrent.hash,
            'name': torrent.name,
            'size': torrent.size,
            'progress': torrent.progress,
            'ratio': torrent.ratio,
            'num_seeds': getattr(torrent, 'num_seeds', 0),
            'num_leechs': getattr(torrent, 'num_leechs', 0),
            'state': torrent.state,
            'category': getattr(torrent, 'category', ''),
            'priority': getattr(torrent, 'priority', 0),
            'downloaded': getattr(torrent, 'downloaded', 0),
            'uploaded': getattr(torrent, 'uploaded', 0),
            'dlspeed': getattr(torrent, 'dlspeed', 0),
            'upspeed': getattr(torrent, 'upspeed', 0),
            'eta': getattr(torrent, 'eta', 0),
            'added_on': getattr(torrent, 'added_on', 0),
            'completion_on': getattr(torrent, 'completion_on', 0),
            # Additional fields that might be useful
            'seeds_total': getattr(torrent, 'seeds_total', 0),
            'peers_total': getattr(torrent, 'peers_total', 0),
            'force_start': getattr(torrent, 'force_start', False)
        }

    def check_and_manage_torrents(self):
        """Main logic to check and manage torrents based on seeding criteria"""
        if not self.qb_client:
            if not self.connect_qbittorrent():
                return

        try:
            torrents = self.get_torrents_by_category()
            self.last_check = datetime.now()

            total_ratio = 0
            total_peers = 0
            total_seeders = 0
            paused_count = 0
            seeding_count = 0

            for torrent in torrents:
                total_ratio += torrent['ratio']
                # Use the correct field names for peers and seeders
                total_peers += torrent.get('num_leechs', 0)
                total_seeders += torrent.get('num_seeds', 0)

                # Count different states
                if torrent['state'] in ['pausedUP', 'pausedDL']:
                    paused_count += 1
                    # Resume paused torrent to refresh peer/seed info
                    self._resume_torrent(torrent['hash'])
                    time.sleep(2)  # Wait for peer refresh

                elif torrent['state'] in ['uploading', 'stalledUP', 'queuedUP']:
                    seeding_count += 1
                    # Check if torrent should be paused
                    if (torrent.get('num_seeds', 0) >= self.config['seed_limit'] and
                        torrent['ratio'] >= self.config['min_ratio']):
                        self._pause_torrent(torrent['hash'])
                        logger.info(f"Paused torrent: {torrent['name']} "
                                  f"(Seeds: {torrent.get('num_seeds', 0)}, Ratio: {torrent['ratio']:.2f})")

            # Update stats
            self.stats.update({
                'total_torrents': len(torrents),
                'paused_torrents': paused_count,
                'seeding_torrents': seeding_count,
                'avg_ratio': total_ratio / len(torrents) if torrents else 0,
                'total_peers': total_peers,
                'total_seeders': total_seeders
            })

            logger.info(f"Checked {len(torrents)} torrents. "
                       f"Paused: {paused_count}, Seeding: {seeding_count}")

        except Exception as e:
            logger.error(f"Error during torrent check: {e}")

    def _pause_torrent(self, torrent_hash: str):
        """Pause a specific torrent"""
        try:
            self.qb_client.torrents_pause(torrent_hashes=torrent_hash)
        except Exception as e:
            logger.error(f"Failed to pause torrent {torrent_hash}: {e}")

    def _resume_torrent(self, torrent_hash: str):
        """Resume a specific torrent"""
        try:
            self.qb_client.torrents_resume(torrent_hashes=torrent_hash)
        except Exception as e:
            logger.error(f"Failed to resume torrent {torrent_hash}: {e}")

    def force_check_torrent(self, torrent_hash: str):
        """Force recheck a specific torrent"""
        try:
            self.qb_client.torrents_recheck(torrent_hashes=torrent_hash)
            logger.info(f"Force checking torrent: {torrent_hash}")
        except Exception as e:
            logger.error(f"Failed to force check torrent {torrent_hash}: {e}")

    def force_start_torrent(self, torrent_hash: str):
        """Force start a specific torrent (bypass queue)"""
        try:
            self.qb_client.torrents_set_force_start(torrent_hashes=torrent_hash, value=True)
            logger.info(f"Force starting torrent: {torrent_hash}")
        except Exception as e:
            logger.error(f"Failed to force start torrent {torrent_hash}: {e}")

    def update_config(self, new_config: Dict):
        """Update configuration settings"""
        self.config.update(new_config)

        # Save to config.json
        with open('config.json', 'w') as f:
            json.dump(self.config, f, indent=2)

        logger.info("Configuration updated")

# Global seed manager instance
seed_manager = SeedManager()

# Flask Routes
@app.route('/')
def index():
    """Serve the main page"""
    return render_template('index.html')

@app.route('/favicon.ico')
def favicon():
    """Return a simple favicon response"""
    return '', 204  # No content response

@app.route('/api/status')
def api_status():
    """Get current application status"""
    return jsonify({
        'connected': seed_manager.qb_client is not None,
        'last_check': seed_manager.last_check.isoformat() if seed_manager.last_check else None,
        'config': seed_manager.config,
        'stats': seed_manager.stats
    })

@app.route('/api/torrents')
def api_torrents():
    """Get all torrents in the configured category"""
    torrents = seed_manager.get_torrents_by_category()
    return jsonify(torrents)

@app.route('/api/connect', methods=['POST'])
def api_connect():
    """Test connection to qBittorrent"""
    success = seed_manager.connect_qbittorrent()
    return jsonify({'success': success})

@app.route('/api/check', methods=['POST'])
def api_check():
    """Manually trigger torrent check"""
    seed_manager.check_and_manage_torrents()
    return jsonify({'success': True, 'message': 'Check completed'})

@app.route('/api/torrent/<torrent_hash>/resume', methods=['POST'])
def api_resume_torrent(torrent_hash):
    """Resume a specific torrent"""
    seed_manager._resume_torrent(torrent_hash)
    return jsonify({'success': True, 'message': 'Torrent resumed'})

@app.route('/api/torrent/<torrent_hash>/pause', methods=['POST'])
def api_pause_torrent(torrent_hash):
    """Pause a specific torrent"""
    seed_manager._pause_torrent(torrent_hash)
    return jsonify({'success': True, 'message': 'Torrent paused'})

@app.route('/api/torrent/<torrent_hash>/force-check', methods=['POST'])
def api_force_check_torrent(torrent_hash):
    """Force check a specific torrent"""
    seed_manager.force_check_torrent(torrent_hash)
    return jsonify({'success': True, 'message': 'Force check initiated'})

@app.route('/api/torrent/<torrent_hash>/force-start', methods=['POST'])
def api_force_start_torrent(torrent_hash):
    """Force start a specific torrent"""
    seed_manager.force_start_torrent(torrent_hash)
    return jsonify({'success': True, 'message': 'Force start initiated'})

@app.route('/api/config', methods=['GET', 'POST'])
def api_config():
    """Get or update configuration"""
    if request.method == 'GET':
        return jsonify(seed_manager.config)

    elif request.method == 'POST':
        try:
            new_config = request.get_json()
            if not new_config:
                return jsonify({'success': False, 'message': 'No JSON data provided'}), 400

            seed_manager.update_config(new_config)
            return jsonify({'success': True, 'message': 'Configuration updated'})
        except Exception as e:
            logger.error(f"Failed to update config: {e}")
            return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/categories')
def api_categories():
    """Get all available categories from qBittorrent"""
    if not seed_manager.qb_client:
        return jsonify({'success': False, 'message': 'Not connected to qBittorrent'})

    try:
        categories = seed_manager.qb_client.torrents_categories()
        category_list = list(categories.keys()) if categories else []

        # Also get categories from existing torrents
        torrents = seed_manager.qb_client.torrents_info()
        torrent_categories = set()
        for torrent in torrents:
            if torrent.category and torrent.category.strip():
                torrent_categories.add(torrent.category)

        # Combine both lists and remove duplicates
        all_categories = list(set(category_list + list(torrent_categories)))
        all_categories.sort()

        return jsonify({
            'success': True,
            'categories': all_categories,
            'current': seed_manager.config['category']
        })
    except Exception as e:
        logger.error(f"Failed to get categories: {e}")
        return jsonify({'success': False, 'message': str(e)})



def run_scheduler():
    """Run the scheduled torrent checks in a separate thread"""
    schedule.every(seed_manager.config['check_interval']).seconds.do(
        seed_manager.check_and_manage_torrents
    )

    while True:
        schedule.run_pending()
        time.sleep(1)

if __name__ == '__main__':
    # Connect to qBittorrent on startup
    seed_manager.connect_qbittorrent()

    # Start scheduler in background thread
    scheduler_thread = threading.Thread(target=run_scheduler, daemon=True)
    scheduler_thread.start()

    app.run(debug=True, host='0.0.0.0', port=5000)
